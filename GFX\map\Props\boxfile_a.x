xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
 <b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}

template AnimTicksPerSecond {
 <9e415a43-7ba6-4a73-8743-b73d47e88476>
 DWORD AnimTicksPerSecond;
}

template FVFData {
 <b6e70a0e-8ef9-4e83-94ad-ecc8b0c04897>
 DWORD dwFVF;
 DWORD nDWords;
 array DWORD data[nDWords];
}


AnimTicksPerSecond {
 24;
}

Frame Body {
 

 FrameTransformMatrix {
  1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000;;
 }

 Mesh Body {
  24;
  2.224800;18.319014;-7.052299;,
  2.224800;18.319014;7.052303;,
  -2.224800;18.319014;7.052303;,
  -2.224800;18.319014;-7.052299;,
  2.224800;0.000000;-7.052299;,
  2.224800;0.000000;7.052204;,
  2.224800;18.319014;7.052303;,
  2.224800;18.319014;-7.052299;,
  -2.224800;0.000000;-7.052299;,
  -2.224800;0.000000;7.052204;,
  2.224800;0.000000;7.052204;,
  2.224800;0.000000;-7.052299;,
  -2.224800;18.319014;-7.052299;,
  -2.224800;18.319014;7.052303;,
  -2.224800;0.000000;7.052204;,
  -2.224800;0.000000;-7.052299;,
  2.224800;18.319014;7.052303;,
  2.224800;0.000000;7.052204;,
  -2.224800;0.000000;7.052204;,
  -2.224800;18.319014;7.052303;,
  -2.224800;18.319014;-7.052299;,
  -2.224800;0.000000;-7.052299;,
  2.224800;0.000000;-7.052299;,
  2.224800;18.319014;-7.052299;;
  12;
  3;0,2,1;,
  3;0,3,2;,
  3;4,6,5;,
  3;4,7,6;,
  3;8,10,9;,
  3;8,11,10;,
  3;12,14,13;,
  3;12,15,14;,
  3;16,18,17;,
  3;16,19,18;,
  3;20,22,21;,
  3;20,23,22;;

  MeshNormals {
   24;
   0.577350;0.577350;-0.577350;,
   0.577351;0.577348;0.577351;,
   -0.577351;0.577348;0.577351;,
   -0.577350;0.577350;-0.577350;,
   0.577350;-0.577350;-0.577350;,
   0.577349;-0.577352;0.577349;,
   0.577351;0.577348;0.577351;,
   0.577350;0.577350;-0.577350;,
   -0.577350;-0.577350;-0.577350;,
   -0.577349;-0.577352;0.577349;,
   0.577349;-0.577352;0.577349;,
   0.577350;-0.577350;-0.577350;,
   -0.577350;0.577350;-0.577350;,
   -0.577351;0.577348;0.577351;,
   -0.577349;-0.577352;0.577349;,
   -0.577350;-0.577350;-0.577350;,
   0.577351;0.577348;0.577351;,
   0.577349;-0.577352;0.577349;,
   -0.577349;-0.577352;0.577349;,
   -0.577351;0.577348;0.577351;,
   -0.577350;0.577350;-0.577350;,
   -0.577350;-0.577350;-0.577350;,
   0.577350;-0.577350;-0.577350;,
   0.577350;0.577350;-0.577350;;
   12;
   3;0,2,1;,
   3;0,3,2;,
   3;4,6,5;,
   3;4,7,6;,
   3;8,10,9;,
   3;8,11,10;,
   3;12,14,13;,
   3;12,15,14;,
   3;16,18,17;,
   3;16,19,18;,
   3;20,22,21;,
   3;20,23,22;;
  }

  MeshTextureCoords {
   24;
   0.633046;0.356894;,
   0.633046;0.069380;,
   0.551681;0.069380;,
   0.551681;0.356894;,
   0.516444;0.430326;,
   0.774361;0.430326;,
   0.774363;0.056903;,
   0.516444;0.056903;,
   0.639446;0.069380;,
   0.639446;0.356892;,
   0.720812;0.356892;,
   0.720812;0.069380;,
   0.802791;0.053109;,
   0.544873;0.053109;,
   0.544874;0.426531;,
   0.802791;0.426531;,
   0.780763;0.056903;,
   0.780763;0.430326;,
   0.862129;0.430326;,
   0.862129;0.056903;,
   0.033710;0.049293;,
   0.033710;0.659292;,
   0.167302;0.659292;,
   0.167302;0.049293;;
  }

  VertexDuplicationIndices {
   24;
   24;
   0,
   1,
   2,
   3,
   4,
   5,
   6,
   7,
   8,
   9,
   10,
   11,
   12,
   13,
   14,
   15,
   16,
   17,
   18,
   19,
   20,
   21,
   22,
   23;
  }

  MeshMaterialList {
   1;
   12;
   0,
   0,
   0,
   0,
   0,
   0,
   0,
   0,
   0,
   0,
   0,
   0;

   Material def_surf_mat {
    0.992157;0.992157;0.992157;1.000000;;
    128.000000;
    0.149020;0.149020;0.149020;;
    0.000000;0.000000;0.000000;;

    TextureFilename {
     "boxfile_a.jpg";
    }
   }
  }

  XSkinMeshHeader {
   1;
   1;
   1;
  }

  SkinWeights {
   "Body";
   24;
   0,
   1,
   2,
   3,
   4,
   5,
   6,
   7,
   8,
   9,
   10,
   11,
   12,
   13,
   14,
   15,
   16,
   17,
   18,
   19,
   20,
   21,
   22,
   23;
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000,
   1.000000;
   1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000;;
  }
 }
}