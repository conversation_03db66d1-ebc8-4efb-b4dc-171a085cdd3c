.lib "kernel32.dll"

GlobalMemoryStatus(buffer*)

ptr_helper__%(a*,b%,c%):"MulDiv"

apiRtlMoveMemory(Destination*,Source,Length):"RtlMoveMemory"
apiRtlMoveMemory2(Destination,Source*,Length):"RtlMoveMemory"


api_AddAtom% (lpString$) : "AddAtomA"
api_AllocConsole% () : "AllocConsole"
api_BackupRead% (hFile%, lpBuffer%, nNumberOfBytesToRead%, lpNumberOfBytesRead%, bAbort%, bProcessSecurity%, lpContext*) : "BackupRead"
api_BackupSeek% (hFile%, dwLowBytesToSeek%, dwHighBytesToSeek%, lpdwLowByteSeeked%, lpdwHighByteSeeked%, lpContext%) : "BackupSeek"
api_BackupWrite% (hFile%, lpBuffer%, nNumberOfBytesToWrite%, lpNumberOfBytesWritten%, bAbort%, bProcessSecurity%, lpContext%) : "BackupWrite"
api_Beep% (dwFreq%, dwDuration%) : "Beep"
api_BeginUpdateResource% (pFileName$, bDeleteExistingResources%) : "BeginUpdateResourceA"
api_BuildCommDCB% (lpDef$, lpDCB*) : "BuildCommDCBA"
api_BuildCommDCBAndTimeouts% (lpDef$, lpDCB*, lpCommTimeouts*) : "BuildCommDCBAndTimeoutsA"
api_CallNamedPipe% (lpNamedPipeName$, lpInBuffer*, nInBufferSize%, lpOutBuffer*, nOutBufferSize%, lpBytesRead%, nTimeOut%) : "CallNamedPipeA"
api_ClearCommBreak% (nCid%) : "ClearCommBreak"
api_ClearCommError% (hFile%, lpErrors%, lpStat*) : "ClearCommError"
api_CloseHandle% (hObject%) : "CloseHandle"
api_CommConfigDialog% (lpszName$, hWnd%, lpCC*) : "CommConfigDialogA"
api_CompareFileTime% (lpFileTime1*, lpFileTime2*) : "CompareFileTime"
api_CompareString% (Locale%, dwCmpFlags%, lpString1$, cchCount1%, lpString2$, cchCount2%) : "CompareStringA"
api_ConnectNamedPipe% (hNamedPipe%, lpOverlapped*) : "ConnectNamedPipe"
api_ContinueDebugEvent% (dwProcessId%, dwThreadId%, dwContinueStatus%) : "ContinueDebugEvent"
api_ConvertDefaultLocale% (Locale%) : "ConvertDefaultLocale"
api_CopyFile% (lpExistingFileName$, lpNewFileName$, bFailIfExists%) : "CopyFileA"
api_CreateConsoleScreenBuffer% (dwDesiredAccess%, dwShareMode%, lpSecurityAttributes*, dwFlags%, lpScreenBufferData*) : "CreateConsoleScreenBuffer"
api_CreateDirectory% (lpPathName$, lpSecurityAttributes*) : "CreateDirectoryA"
api_CreateDirectoryEx% (lpTemplateDirectory$, lpNewDirectory$, lpSecurityAttributes*) : "CreateDirectoryExA"
api_CreateEvent% (lpEventAttributes*, bManualReset%, bInitialState%, lpName$) : "CreateEventA"
api_CreateFile% (lpFileName$, dwDesiredAccess%, dwShareMode%, lpSecurityAttributes*, dwCreationDisposition%, dwFlagsAndAttributes%, hTemplateFile%) : "CreateFileA"
api_CreateFileMapping% (hFile%, lpFileMappigAttributes*, flProtect%, dwMaximumSizeHigh%, dwMaximumSizeLow%, lpName$) : "CreateFileMappingA"
api_CreateIoCompletionPort% (FileHandle%, ExistingCompletionPort%, CompletionKey%, NumberOfConcurrentThreads%) : "CreateIoCompletionPort"
api_CreateMailslot% (lpName$, nMaxMessageSize%, lReadTimeout%, lpSecurityAttributes*) : "CreateMailslotA"
api_CreateMutex% (lpMutexAttributes*, bInitialOwner%, lpName$) : "CreateMutexA"
api_CreateNamedPipe% (lpName$, dwOpenMode%, dwPipeMode%, nMaxInstances%, nOutBufferSize%, nInBufferSize%, nDefaultTimeOut%, lpSecurityAttributes*) : "CreateNamedPipeA"
api_CreatePipe% (phReadPipe%, phWritePipe%, lpPipeAttributes*, nSize%) : "CreatePipe"
api_CreateProcess% (lpApplicationName$, lpCommandLine$, lpProcessAttributes*, lpThreadAttributes*, bInheritHandles%, dwCreationFlags%, lpEnvironment*, lpCurrentDriectory$, lpStartupInfo*, lpProcessInformation*) : "CreateProcessA"
api_CreateProcessAsUser% (hToken%, lpApplicationName$, lpCommandLine$, lpProcessAttributes*, lpThreadAttributes*, bInheritHandles%, dwCreationFlags%, lpEnvironment$, lpCurrentDirectory$, lpStartupInfo*, lpProcessInformation*) : "CreateProcessAsUserA"
api_CreateRemoteThread% (hProcess%, lpThreadAttributes*, dwStackSize%, lpStartAddress%, lpParameter*, dwCreationFlags%, lpThreadId%) : "CreateRemoteThread"
api_CreateSemaphore% (lpSemaphoreAttributes*, lInitialCount%, lMaximumCount%, lpName$) : "CreateSemaphoreA"
api_CreateTapePartition% (hDevice%, dwPartitionMethod%, dwCount%, dwSize%) : "CreateTapePartition"
api_CreateThread% (lpThreadAttributes*, dwStackSize%, lpStartAddress%, lpParameter*, dwCreationFlags%, lpThreadId%) : "CreateThread"
api_DebugActiveProcess% (dwProcessId%) : "DebugActiveProcess"
api_DebugBreak () : "DebugBreak"
api_DefineDosDevice% (dwFlags%, lpDeviceName$, lpTargetPath$) : "DefineDosDeviceA"
api_DeleteAtom% (nAtom%) : "DeleteAtom"
api_DeleteCriticalSection (lpCriticalSection*) : "DeleteCriticalSection"
api_DeleteFile% (lpFileName$) : "DeleteFileA"
api_DeviceIoControl% (hDevice%, dwIoControlCode%, lpInBuffer*, nInBufferSize%, lpOutBuffer*, nOutBufferSize%, lpBytesReturned%, lpOverlapped*) : "DeviceIoControl"
api_DisableThreadLibraryCalls% (hLibModule%) : "DisableThreadLibraryCalls"
api_DisconnectNamedPipe% (hNamedPipe%) : "DisconnectNamedPipe"
api_DosDateTimeToFileTime% (wFatDate%, wFatTime%, lpFileTime*) : "DosDateTimeToFileTime"
api_DuplicateHandle% (hSourceProcessHandle%, hSourceHandle%, hTargetProcessHandle%, lpTargetHandle%, dwDesiredAccess%, bInheritHandle%, dwOptions%) : "DuplicateHandle"
api_EndUpdateResource% (hUpdate%, fDiscard%) : "EndUpdateResourceA"
api_EnterCriticalSection (lpCriticalSection*) : "EnterCriticalSection"
api_EnumCalendarInfo% (lpCalInfoEnumProc%, Locale%, Calendar%, CalType%) : "EnumCalendarInfoA"
api_EnumDateFormats% (lpDateFmtEnumProc%, Locale%, dwFlags%) : "EnumDateFormats"
api_EnumResourceLanguages% (hModule%, lpType$, lpName$, lpEnumFunc%, lParam%) : "EnumResourceLanguagesA"
api_EnumResourceNames% (hModule%, lpType$, lpEnumFunc%, lParam%) : "EnumResourceNamesA"
api_EnumResourceTypes% (hModule%, lpEnumFunc%, lParam%) : "EnumResourceTypesA"
api_EnumSystemCodePages% (lpCodePageEnumProc%, dwFlags%) : "EnumSystemCodePages"
api_EnumSystemLocales% (lpLocaleEnumProc%, dwFlags%) : "EnumSystemLocales"
api_EnumTimeFormats% (lpTimeFmtEnumProc%, Locale%, dwFlags%) : "EnumTimeFormats"
api_EraseTape% (hDevice%, dwEraseType%, bimmediate%) : "EraseTape"
api_EscapeCommFunction% (nCid%, nFunc%) : "EscapeCommFunction"
api_ExitProcess (uExitCode%) : "ExitProcess"
api_ExitThread (dwExitCode%) : "ExitThread"
api_ExpandEnvironmentStrings% (lpSrc$, lpDst$, nSize%) : "ExpandEnvironmentStringsA"
api_FatalAppExit (uAction%, lpMessageText$) : "FatalAppExitA"
api_FatalExit (code%) : "FatalExit"
api_FileTimeToDosDateTime% (lpFileTime*, lpFatDate%, lpFatTime%) : "FileTimeToDosDateTime"
api_FileTimeToLocalFileTime% (lpFileTime*, lpLocalFileTime*) : "FileTimeToLocalFileTime"
api_FileTimeToSystemTime% (lpFileTime*, lpSystemTime*) : "FileTimeToSystemTime"
api_FillConsoleOutputAttribute% (hConsoleOutput%, wAttribute%, nLength%, dwWriteCoord*, lpNumberOfAttrsWritten%) : "FillConsoleOutputAttribute"
api_FillConsoleOutputCharacter% (hConsoleOutput%, cCharacter%, nLength%, dwWriteCoord*, lpNumberOfCharsWritten%) : "FillConsoleOutputCharacterA"
api_FindAtom% (lpString$) : "FindAtomA"
api_FindClose% (hFindFile%) : "FindClose"
api_FindCloseChangeNotification% (hChangeHandle%) : "FindCloseChangeNotification"
api_FindFirstChangeNotification% (lpPathName$, bWatchSubtree%, dwNotifyFilter%) : "FindFirstChangeNotificationA"
api_FindFirstFile% (lpFileName$, lpFindFileData*) : "FindFirstFileA"
api_FindNextChangeNotification% (hChangeHandle%) : "FindNextChangeNotification"
api_FindNextFile% (hFindFile%, lpFindFileData*) : "FindNextFileA"
api_FindResource% (hInstance%, lpName$, lpType$) : "FindResourceA"
api_FindResourceEx% (hModule%, lpType$, lpName$, wLanguage%) : "FindResourceExA"
api_FlushConsoleInputBuffer% (hConsoleInput%) : "FlushConsoleInputBuffer"
api_FlushFileBuffers% (hFile%) : "FlushFileBuffers"
api_FlushInstructionCache% (hProcess%, lpBaseAddress*, dwSize%) : "FlushInstructionCache"
api_FlushViewOfFile% (lpBaseAddress*, dwNumberOfBytesToFlush%) : "FlushViewOfFile"
api_FoldString% (dwMapFlags%, lpSrcStr$, cchSrc%, lpDestStr$, cchDest%) : "FoldStringA"
api_FormatMessage% (dwFlags%, lpSource*, dwMessageId%, dwLanguageId%, lpBuffer$, nSize%, Arguments%) : "FormatMessageA"
api_FreeConsole% () : "FreeConsole"
api_FreeEnvironmentStrings% (lpsz$) : "FreeEnvironmentStringsA"
api_FreeLibrary% (hLibModule%) : "FreeLibrary"
api_FreeLibraryAndExitThread (hLibModule%, dwExitCode%) : "FreeLibraryAndExitThread"
api_FreeResource% (hResData%) : "FreeResource"
api_GenerateConsoleCtrlEvent% (dwCtrlEvent%, dwProcessGroupId%) : "GenerateConsoleCtrlEvent"
api_GetACP% () : "GetACP"
api_GetAtomName% (nAtom%, lpBuffer$, nSize%) : "GetAtomNameA"
api_GetBinaryType% (lpApplicationName$, lpBinaryType%) : "GetBinaryTypeA"
api_GetCommandLine$ () : "GetCommandLineA"
api_GetCommConfig% (hCommDev%, lpCC*, lpdwSize%) : "GetCommConfig"
api_GetCommMask% (hFile%, lpEvtMask%) : "GetCommMask"
api_GetCommModemStatus% (hFile%, lpModemStat%) : "GetCommModemStatus"
api_GetCommProperties% (hFile%, lpCommProp*) : "GetCommProperties"
api_GetCommState% (nCid%, lpDCB*) : "GetCommState"
api_GetCommTimeouts% (hFile%, lpCommTimeouts*) : "GetCommTimeouts"
api_GetCompressedFileSize% (lpFileName$, lpFileSizeHigh%) : "GetCompressedFileSizeA"
api_GetComputerName% (lpBuffer$, nSize%) : "GetComputerNameA"
api_GetConsoleCP% () : "GetConsoleCP"
api_GetConsoleCursorInfo% (hConsoleOutput%, lpConsoleCursorInfo*) : "GetConsoleCursorInfo"
api_GetConsoleMode% (hConsoleHandle%, lpMode%) : "GetConsoleMode"
api_GetConsoleOutputCP% () : "GetConsoleOutputCP"
api_GetConsoleScreenBufferInfo% (hConsoleOutput%, lpConsoleScreenBufferInfo*) : "GetConsoleScreenBufferInfo"
api_GetConsoleTitle% (lpConsoleTitle$, nSize%) : "GetConsoleTitleA"
api_GetCPInfo% (CodePage%, lpCPInfo*) : "GetCPInfo"
api_GetCurrencyFormat% (Locale%, dwFlags%, lpValue$, lpFormat*, lpCurrencyStr$, cchCurrency%) : "GetCurrencyFormatA"
api_GetCurrentDirectory% (nBufferLength%, lpBuffer$) : "GetCurrentDirectory"
api_GetCurrentProcess% () : "GetCurrentProcess"
api_GetCurrentProcessId% () : "GetCurrentProcessId"
api_GetCurrentThread% () : "GetCurrentThread"
api_GetCurrentThreadId% () : "GetCurrentThreadId"
api_GetDateFormat% (Locale%, dwFlags%, lpDate*, lpFormat$, lpDateStr$, cchDate%) : "GetDateFormatA"
api_GetDefaultCommConfig% (lpszName$, lpCC*, lpdwSize%) : "GetDefaultCommConfigA"
api_GetDiskFreeSpace% (lpRootPathName$, lpSectorsPerCluster%, lpBytesPerSector%, lpNumberOfFreeClusters%, lpTtoalNumberOfClusters%) : "GetDiskFreeSpaceA"
api_GetDriveType% (nDrive$) : "GetDriveTypeA"
api_GetEnvironmentStrings$ () : "GetEnvironmentStringsA"
api_GetEnvironmentVariable% (lpName$, lpBuffer$, nSize%) : "GetEnvironmentVariableA"
api_GetExitCodeProcess% (hProcess%, lpExitCode%) : "GetExitCodeProcess"
api_GetExitCodeThread% (hThread%, lpExitCode%) : "GetExitCodeThread"
api_GetFileAttributes% (lpFileName$) : "GetFileAttributesA"
api_GetFileInformationByHandle% (hFile%, lpFileInformation*) : "GetFileInformationByHandle"
api_GetFileSize% (hFile%, lpFileSizeHigh%) : "GetFileSize"
api_GetFileTime% (hFile%, lpCreationTime*, lpLastAccessTime*, lpLastWriteTime*) : "GetFileTime"
api_GetFileType% (hFile%) : "GetFileType"
api_GetFullPathName% (lpFileName$, nBufferLength%, lpBuffer$, lpFilePart$) : "GetFullPathNameA"
api_GetHandleInformation% (hObject%, lpdwFlags%) : "GetHandleInformation"
api_GetLastError% () : "GetLastError"
api_GetLocaleInfo% (Locale%, LCType%, lpLCData$, cchData%) : "GetLocaleInfoA"
api_GetLocalTime (lpSystemTime*) : "GetLocalTime"
api_GetLogicalDrives% () : "GetLogicalDrives"
api_GetLogicalDriveStrings% (nBufferLength%, lpBuffer$) : "GetLogicalDriveStringsA"
api_GetMailslotInfo% (hMailslot%, lpMaxMessageSize%, lpNextSize%, lpMessageCount%, lpReadTimeout%) : "GetMailslotInfo"
api_GetModuleFileName% (hModule%, lpFileName$, nSize%) : "GetModuleFileNameA"
api_GetModuleHandle% (lpModuleName$) : "GetModuleHandleA"
api_GetNamedPipeHandleState% (hNamedPipe%, lpState%, lpCurInstances%, lpMaxCollectionCount%, lpCollectDataTimeout%, lpUserName$, nMaxUserNameSize%) : "GetNamedPipeHandleStateA"
api_GetNamedPipeInfo% (hNamedPipe%, lpFlags%, lpOutBufferSize%, lpInBufferSize%, lpMaxInstances%) : "GetNamedPipeInfo"
api_GetNumberFormat% (Locale%, dwFlags%, lpValue$, lpFormat*, lpNumberStr$, cchNumber%) : "GetNumberFormatA"
api_GetNumberOfConsoleInputEvents% (hConsoleInput%, lpNumberOfEvents%) : "GetNumberOfConsoleInputEvents"
api_GetNumberOfConsoleMouseButtons% (lpNumberOfMouseButtons%) : "GetNumberOfConsoleMouseButtons"
api_GetOEMCP% () : "GetOEMCP"
api_GetOverlappedResult% (hFile%, lpOverlapped*, lpNumberOfBytesTransferred%, bWait%) : "GetOverlappedResult"
api_GetPriorityClass% (hProcess%) : "GetPriorityClass"
api_GetPrivateProfileInt% (lpApplicationName$, lpKeyName$, nDefault%, lpFileName$) : "GetPrivateProfileIntA"
api_GetPrivateProfileSection% (lpAppName$, lpReturnedString$, nSize%, lpFileName$) : "GetPrivateProfileSectionA"
api_GetPrivateProfileString% (lpApplicationName$, lpKeyName*, lpDefault$, lpReturnedString$, nSize%, lpFileName$) : "GetPrivateProfileStringA"
api_GetProcAddress% (hModule%, lpProcName$) : "GetProcAddress"
api_GetProcessAffinityMask% (hProcess%, lpProcessAffinityMask%, SystemAffinityMask%) : "GetProcessAffinityMask"
api_GetProcessHeap% () : "GetProcessHeap"
api_GetProcessHeaps% (NumberOfHeaps%, ProcessHeaps%) : "GetProcessHeaps"
api_GetProcessShutdownParameters% (lpdwLevel%, lpdwFlags%) : "GetProcessShutdownParameters"
api_GetProcessTimes% (hProcess%, lpCreationTime*, lpExitTime*, lpKernelTime*, lpUserTime*) : "GetProcessTimes"
api_GetProcessWorkingSetSize% (hProcess%, lpMinimumWorkingSetSize%, lpMaximumWorkingSetSize%) : "GetProcessWorkingSetSize"
api_GetProfileInt% (lpAppName$, lpKeyName$, nDefault%) : "GetProfileIntA"
api_GetProfileSection% (lpAppName$, lpReturnedString$, nSize%) : "GetProfileSectionA"
api_GetProfileString% (lpAppName$, lpKeyName$, lpDefault$, lpReturnedString$, nSize%) : "GetProfileStringA"
api_GetQueuedCompletionStatus% (CompletionPort%, lpNumberOfBytesTransferred%, lpCompletionKey%, lpOverlapped%, dwMilliseconds%) : "GetQueuedCompletionStatus"
api_GetShortPathName% (lpszLongPath$, lpszShortPath$, cchBuffer%) : "GetShortPathName"
api_GetStartupInfo (lpStartupInfo*) : "GetStartupInfoA"
api_GetStdHandle% (nStdHandle%) : "GetStdHandle"
api_GetStringTypeA% (lcid%, dwInfoType%, lpSrcStr$, cchSrc%, lpCharType%) : "GetStringTypeA"
api_GetStringTypeEx% (Locale%, dwInfoType%, lpSrcStr$, cchSrc%, lpCharType%) : "GetStringTypeExA"
api_GetStringTypeW% (dwInfoType%, lpSrcStr$, cchSrc%, lpCharType%) : "GetStringTypeW"
api_GetSystemDefaultLangID% () : "GetSystemDefaultLangID"
api_GetSystemDefaultLCID% () : "GetSystemDefaultLCID"
api_GetSystemDirectory% (lpBuffer$, nSize%) : "GetSystemDirectoryA"
api_GetSystemInfo (lpSystemInfo*) : "GetSystemInfo"
api_GetSystemPowerStatus% (lpSystemPowerStatus*) : "GetSystemPowerStatus"
api_GetSystemTime (lpSystemTime*) : "GetSystemTime"
api_GetSystemTimeAdjustment% (lpTimeAdjustment%, lpTimeIncrement%, lpTimeAdjustmentDisabled%) : "GetSystemTimeAdjustment"
api_GetTapeParameters% (hDevice%, dwOperation%, lpdwSize%, lpTapeInformation*) : "GetTapeParameters"
api_GetTapePosition% (hDevice%, dwPositionType%, lpdwPartition%, lpdwOffsetLow%, lpdwOffsetHigh%) : "GetTapePosition"
api_GetTapeStatus% (hDevice%) : "GetTapeStatus"
api_GetTempFileName% (lpszPath$, lpPrefixString$, wUnique%, lpTempFileName$) : "GetTempFileNameA"
api_GetTempPath% (nBufferLength%, lpBuffer$) : "GetTempPathA"
api_GetThreadContext% (hThread%, lpContext*) : "GetThreadContext"
api_GetThreadLocale% () : "GetThreadLocale"
api_GetThreadPriority% (hThread%) : "GetThreadPriority"
api_GetThreadSelectorEntry% (hThread%, dwSelector%, lpSelectorEntry*) : "GetThreadSelectorEntry"
api_GetThreadTimes% (hThread%, lpCreationTime*, lpExitTime*, lpKernelTime*, lpUserTime*) : "GetThreadTimes"
api_GetTickCount% () : "GetTickCount"
api_GetTimeFormat% (Locale%, dwFlags%, lpTime*, lpFormat$, lpTimeStr$, cchTime%) : "GetTimeFormatA"
api_GetTimeZoneInformation% (lpTimeZoneInformation*) : "GetTimeZoneInformation"
api_GetUserDefaultLangID% () : "GetUserDefaultLangID"
api_GetUserDefaultLCID% () : "GetUserDefaultLCID"
api_GetVersion% () : "GetVersion"
api_GetVersionEx% (lpVersionInformation*) : "GetVersionExA"
api_GetVolumeInformation% (lpRootPathName$, lpVolumeNameBuffer$, nVolumeNameSize%, lpVolumeSerialNumber%, lpMaximumComponentLength%, lpFileSystemFlags%, lpFileSystemNameBuffer$, nFileSystemNameSize%) : "GetVolumeInformationA"
api_GetWindowsDirectory% (lpBuffer$, nSize%) : "GetWindowsDirectoryA"
api_GlobalAddAtom% (lpString$) : "GlobalAddAtomA"
api_GlobalAlloc% (wFlags%, dwBytes%) : "GlobalAlloc"
api_GlobalCompact% (dwMinFree%) : "GlobalCompact"
api_GlobalDeleteAtom% (nAtom%) : "GlobalDeleteAtom"
api_GlobalFindAtom% (lpString$) : "GlobalFindAtomA"
api_GlobalFix (hMem%) : "GlobalFix"
api_GlobalFlags% (hMem%) : "GlobalFlags"
api_GlobalFree% (hMem%) : "GlobalFree"
api_GlobalGetAtomName% (nAtom%, lpBuffer$, nSize%) : "GlobalGetAtomNameA"
api_GlobalHandle% (wMem*) : "GlobalHandle"
api_GlobalLock% (hMem%) : "GlobalLock"
api_GlobalMemoryStatus (lpBuffer*) : "GlobalMemoryStatus"
api_GlobalReAlloc% (hMem%, dwBytes%, wFlags%) : "GlobalReAlloc"
api_GlobalSize% (hMem%) : "GlobalSize"
api_GlobalUnfix (hMem%) : "GlobalUnfix"
api_GlobalUnlock% (hMem%) : "GlobalUnlock"
api_GlobalUnWire% (hMem%) : "GlobalUnWire"
api_GlobalWire% (hMem%) : "GlobalWire"
api_HeapAlloc% (hHeap%, dwFlags%, dwBytes%) : "HeapAlloc"
api_HeapCompact% (hHeap%, dwFlags%) : "HeapCompact"
api_HeapCreate% (flOptions%, dwInitialSize%, dwMaximumSize%) : "HeapCreate"
api_HeapDestroy% (hHeap%) : "HeapDestroy"
api_HeapFree% (hHeap%, dwFlags%, lpMem*) : "HeapFree"
api_HeapLock% (hHeap%) : "HeapLock"
api_HeapReAlloc% (hHeap%, dwFlags%, lpMem*, dwBytes%) : "HeapReAlloc"
api_HeapSize% (hHeap%, dwFlags%, lpMem*) : "HeapSize"
api_HeapUnlock% (hHeap%) : "HeapUnlock"
api_HeapValidate% (hHeap%, dwFlags%, lpMem*) : "HeapValidate"
api_hread% (hFile%, lpBuffer*, lBytes%) : "_hread"
api_hwrite% (hFile%, lpBuffer$, lBytes%) : "_hwrite"
api_ImpersonateLoggedOnUser% (hToken%) : "ImpersonateLoggedOnUser"
api_InitAtomTable% (nSize%) : "InitAtomTable"
api_InitializeCriticalSection (lpCriticalSection*) : "InitializeCriticalSection"
api_InterlockedDecrement% (lpAddend%) : "InterlockedDecrement"
api_InterlockedExchange% (Target%, Value%) : "InterlockedExchange"
api_InterlockedIncrement% (lpAddend%) : "InterlockedIncrement"
api_IsBadCodePtr% (lpfn%) : "IsBadCodePtr"
api_IsBadHugeReadPtr% (lp*, ucb%) : "IsBadHugeReadPtr"
api_IsBadHugeWritePtr% (lp*, ucb%) : "IsBadHugeWritePtr"
api_IsBadReadPtr% (lp*, ucb%) : "IsBadReadPtr"
api_IsBadStringPtr% (lpsz$, ucchMax%) : "IsBadStringPtrA"
api_IsBadWritePtr% (lp*, ucb%) : "IsBadWritePtr"
api_IsDBCSLeadByte% (TestChar%) : "IsDBCSLeadByte"
api_IsValidCodePage% (CodePage%) : "IsValidCodePage"
api_IsValidLocale% (Locale%, dwFlags%) : "IsValidLocale"
api_lclose% (hFile%) : "_lclose"
api_LCMapString% (Locale%, dwMapFlags%, lpSrcStr$, cchSrc%, lpDestStr$, cchDest%) : "LCMapStringA"
api_lcreat% (lpPathName$, iAttribute%) : "_lcreat"
api_LeaveCriticalSection (lpCriticalSection*) : "LeaveCriticalSection"
api_llseek% (hFile%, lOffset%, iOrigin%) : "_llseek"
api_LoadLibrary% (lpLibFileName$) : "LoadLibraryA"
api_LoadLibraryEx% (lpLibFileName$, hFile%, dwFlags%) : "LoadLibraryExA"
api_LoadModule% (lpModuleName$, lpParameterBlock*) : "LoadModule"
api_LoadResource% (hInstance%, hResInfo%) : "LoadResource"
api_LocalAlloc% (wFlags%, wBytes%) : "LocalAlloc"
api_LocalCompact% (uMinFree%) : "LocalCompact"
api_LocalFileTimeToFileTime% (lpLocalFileTime*, lpFileTime*) : "LocalFileTimeToFileTime"
api_LocalFlags% (hMem%) : "LocalFlags"
api_LocalFree% (hMem%) : "LocalFree"
api_LocalHandle% (wMem*) : "LocalHandle"
api_LocalLock% (hMem%) : "LocalLock"
api_LocalReAlloc% (hMem%, wBytes%, wFlags%) : "LocalReAlloc"
api_LocalShrink% (hMem%, cbNewSize%) : "LocalShrink"
api_LocalSize% (hMem%) : "LocalSize"
api_LocalUnlock% (hMem%) : "LocalUnlock"
api_LockFile% (hFile%, dwFileOffsetLow%, dwFileOffsetHigh%, nNumberOfBytesToLockLow%, nNumberOfBytesToLockHigh%) : "LockFile"
api_LockFileEx% (hFile%, dwFlags%, dwReserved%, nNumberOfBytesToLockLow%, nNumberOfBytesToLockHigh%, lpOverlapped*) : "LockFileEx"
api_LockResource% (hResData%) : "LockResource"
api_lopen% (lpPathName$, iReadWrite%) : "_lopen"
api_lread% (hFile%, lpBuffer*, wBytes%) : "_lread"
api_lstrcat% (lpString1$, lpString2$) : "lstrcatA"
api_lstrcmp% (lpString1$, lpString2$) : "lstrcmpA"
api_lstrcmpi% (lpString1$, lpString2$) : "lstrcmpiA"
api_lstrcpy% (lpString1$, lpString2$) : "lstrcpyA"
api_lstrcpyn% (lpString1$, lpString2$, iMaxLength%) : "lstrcpynA"
api_lstrlen% (lpString$) : "lstrlenA"
api_lwrite% (hFile%, lpBuffer$, wBytes%) : "_lwrite"
api_MapViewOfFile% (hFileMappingObject%, dwDesiredAccess%, dwFileOffsetHigh%, dwFileOffsetLow%, dwNumberOfBytesToMap%) : "MapViewOfFile"
api_MapViewOfFileEx% (hFileMappingObject%, dwDesiredAccess%, dwFileOffsetHigh%, dwFileOffsetLow%, dwNumberOfBytesToMap%, lpBaseAddress*) : "MapViewOfFileEx"
api_MoveFile% (lpExistingFileName$, lpNewFileName$) : "MoveFileA"
api_MoveFileEx% (lpExistingFileName$, lpNewFileName$, dwFlags%) : "MoveFileExA"
api_MulDiv% (nNumber%, nNumerator%, nDenominator%) : "MulDiv"
api_MultiByteToWideChar% (CodePage%, dwFlags%, lpMultiByteStr$, cchMultiByte%, lpWideCharStr$, cchWideChar%) : "MultiByteToWideChar"
api_OpenEvent% (dwDesiredAccess%, bInheritHandle%, lpName$) : "OpenEventA"
api_OpenFile% (lpFileName$, lpReOpenBuff*, wStyle%) : "OpenFile"
api_OpenFileMapping% (dwDesiredAccess%, bInheritHandle%, lpName$) : "OpenFileMappingA"
api_OpenMutex% (dwDesiredAccess%, bInheritHandle%, lpName$) : "OpenMutexA"
api_OpenProcess% (dwDesiredAccess%, bInheritHandle%, dwProcessId%) : "OpenProcess"
api_OpenSemaphore% (dwDesiredAccess%, bInheritHandle%, lpName$) : "OpenSemaphoreA"
api_OutputDebugString (lpOutputString$) : "OutputDebugStringA"
api_PeekNamedPipe% (hNamedPipe%, lpBuffer*, nBufferSize%, lpBytesRead%, lpTotalBytesAvail%, lpBytesLeftThisMessage%) : "PeekNamedPipe"
api_PrepareTape% (hDevice%, dwOperation%, bimmediate%) : "PrepareTape"
api_PulseEvent% (hEvent%) : "PulseEvent"
api_PurgeComm% (hFile%, dwFlags%) : "PurgeComm"
api_QueryDosDevice% (lpDeviceName$, lpTargetPath$, ucchMax%) : "QueryDosDeviceA"
api_QueryPerformanceCounter% (lpPerformanceCount*) : "QueryPerformanceCounter"
api_QueryPerformanceFrequency% (lpFrequency*) : "QueryPerformanceFrequency"
api_RaiseException (dwExceptionCode%, dwExceptionFlags%, nNumberOfArguments%, lpArguments%) : "RaiseException"
api_ReadConsole% (hConsoleInput%, lpBuffer*, nNumberOfCharsToRead%, lpNumberOfCharsRead%, lpReserved*) : "ReadConsoleA"
api_ReadConsoleOutput% (hConsoleOutput%, lpBuffer*, dwBufferSize*, dwBufferCoord*, lpReadRegion*) : "ReadConsoleOutputA"
api_ReadConsoleOutputAttribute% (hConsoleOutput%, lpAttribute%, nLength%, dwReadCoord*, lpNumberOfAttrsRead%) : "ReadConsoleOutputAttribute"
api_ReadConsoleOutputCharacter% (hConsoleOutput%, lpCharacter$, nLength%, dwReadCoord*, lpNumberOfCharsRead%) : "ReadConsoleOutputCharacterA"
api_ReadFile% (hFile%, lpBuffer*, nNumberOfBytesToRead%, lpNumberOfBytesRead%, lpOverlapped*) : "ReadFile"
api_ReadFileEx% (hFile%, lpBuffer*, nNumberOfBytesToRead%, lpOverlapped*, lpCompletionRoutine%) : "ReadFileEx"
api_ReadProcessMemory% (hProcess%, lpBaseAddress*, lpBuffer*, nSize%, lpNumberOfBytesWritten%) : "ReadProcessMemory"
api_ReleaseMutex% (hMutex%) : "ReleaseMutex"
api_ReleaseSemaphore% (hSemaphore%, lReleaseCount%, lpPreviousCount%) : "ReleaseSemaphore"
api_RemoveDirectory% (lpPathName$) : "RemoveDirectoryA"
api_ResetEvent% (hEvent%) : "ResetEvent"
api_ResumeThread% (hThread%) : "ResumeThread"
api_ScrollConsoleScreenBuffer% (hConsoleOutput%, lpScrollRectangle*, lpClipRectangle*, dwDestinationOrigin*, lpFill*) : "ScrollConsoleScreenBufferA"
api_SearchPath% (lpPath$, lpFileName$, lpExtension$, nBufferLength%, lpBuffer$, lpFilePart$) : "SearchPathA"
api_SetCommBreak% (nCid%) : "SetCommBreak"
api_SetCommConfig% (hCommDev%, lpCC*, dwSize%) : "SetCommConfig"
api_SetCommMask% (hFile%, dwEvtMask%) : "SetCommMask"
api_SetCommState% (hCommDev%, lpDCB*) : "SetCommState"
api_SetCommTimeouts% (hFile%, lpCommTimeouts*) : "SetCommTimeouts"
api_SetComputerName% (lpComputerName$) : "SetComputerNameA"
api_SetConsoleActiveScreenBuffer% (hConsoleOutput%) : "SetConsoleActiveScreenBuffer"
api_SetConsoleCP% (wCodePageID%) : "SetConsoleCP"
api_SetConsoleCtrlHandler% (HandlerRoutine%, Add%) : "SetConsoleCtrlHandler"
api_SetConsoleCursorInfo% (hConsoleOutput%, lpConsoleCursorInfo*) : "SetConsoleCursorInfo"
api_SetConsoleCursorPosition% (hConsoleOutput%, dwCursorPosition*) : "SetConsoleCursorPosition"
api_SetConsoleMode% (hConsoleHandle%, dwMode%) : "SetConsoleMode"
api_SetConsoleOutputCP% (wCodePageID%) : "SetConsoleOutputCP"
api_SetConsoleScreenBufferSize% (hConsoleOutput%, dwSize*) : "SetConsoleScreenBufferSize"
api_SetConsoleTextAttribute% (hConsoleOutput%, wAttributes%) : "SetConsoleTextAttribute"
api_SetConsoleTitle% (lpConsoleTitle$) : "SetConsoleTitleA"
api_SetConsoleWindowInfo% (hConsoleOutput%, bAbsolute%, lpConsoleWindow*) : "SetConsoleWindowInfo"
api_SetCurrentDirectory% (lpPathName$) : "SetCurrentDirectoryA"
api_SetDefaultCommConfig% (lpszName$, lpCC*, dwSize%) : "SetDefaultCommConfigA"
api_SetEndOfFile% (hFile%) : "SetEndOfFile"
api_SetEnvironmentVariable% (lpName$, lpValue$) : "SetEnvironmentVariableA"
api_SetErrorMode% (wMode%) : "SetErrorMode"
api_SetEvent% (hEvent%) : "SetEvent"
api_SetFileApisToANSI () : "SetFileApisToANSI"
api_SetFileApisToOEM () : "SetFileApisToOEM"
api_SetFileAttributes% (lpFileName$, dwFileAttributes%) : "SetFileAttributesA"
api_SetFilePointer% (hFile%, lDistanceToMove%, lpDistanceToMoveHigh%, dwMoveMethod%) : "SetFilePointer"
api_SetFileTime% (hFile%, lpCreationTime*, lpLastAccessTime*, lpLastWriteTime*) : "SetFileTime"
api_SetHandleCount% (wNumber%) : "SetHandleCount"
api_SetHandleInformation% (hObject%, dwMask%, dwFlags%) : "SetHandleInformation"
api_SetLastError (dwErrCode%) : "SetLastError"
api_SetLocaleInfo% (Locale%, LCType%, lpLCData$) : "SetLocaleInfoA"
api_SetLocalTime% (lpSystemTime*) : "SetLocalTime"
api_SetMailslotInfo% (hMailslot%, lReadTimeout%) : "SetMailslotInfo"
api_SetNamedPipeHandleState% (hNamedPipe%, lpMode%, lpMaxCollectionCount%, lpCollectDataTimeout%) : "SetNamedPipeHandleState"
api_SetPriorityClass% (hProcess%, dwPriorityClass%) : "SetPriorityClass"
api_SetProcessShutdownParameters% (dwLevel%, dwFlags%) : "SetProcessShutdownParameters"
api_SetProcessWorkingSetSize% (hProcess%, dwMinimumWorkingSetSize%, dwMaximumWorkingSetSize%) : "SetProcessWorkingSetSize"
api_SetStdHandle% (nStdHandle%, nHandle%) : "SetStdHandle"
api_SetSystemPowerState% (fSuspend%, fForce%) : "SetSystemPowerState"
api_SetSystemTime% (lpSystemTime*) : "SetSystemTime"
api_SetSystemTimeAdjustment% (dwTimeAdjustment%, bTimeAdjustmentDisabled%) : "SetSystemTimeAdjustment"
api_SetTapeParameters% (hDevice%, dwOperation%, lpTapeInformation*) : "SetTapeParameters"
api_SetTapePosition% (hDevice%, dwPositionMethod%, dwPartition%, dwOffsetLow%, dwOffsetHigh%, bimmediate%) : "SetTapePosition"
api_SetThreadAffinityMask% (hThread%, dwThreadAffinityMask%) : "SetThreadAffinityMask"
api_SetThreadContext% (hThread%, lpContext*) : "SetThreadContext"
api_SetThreadLocale% (Locale%) : "SetThreadLocale"
api_SetThreadPriority% (hThread%, nPriority%) : "SetThreadPriority"
api_SetTimeZoneInformation% (lpTimeZoneInformation*) : "SetTimeZoneInformation"
api_SetUnhandledExceptionFilter% (lpTopLevelExceptionFilter%) : "SetUnhandledExceptionFilter"
api_SetupComm% (hFile%, dwInQueue%, dwOutQueue%) : "SetupComm"
api_SetVolumeLabel% (lpRootPathName$, lpVolumeName$) : "SetVolumeLabelA"
api_SizeofResource% (hInstance%, hResInfo%) : "SizeofResource"
api_Sleep (dwMilliseconds%) : "Sleep"
api_SleepEx% (dwMilliseconds%, bAlertable%) : "SleepEx"
api_SuspendThread% (hThread%) : "SuspendThread"
api_SystemTimeToFileTime% (lpSystemTime*, lpFileTime*) : "SystemTimeToFileTime"
api_SystemTimeToTzSpecificLocalTime% (lpTimeZoneInformation*, lpUniversalTime*, lpLocalTime*) : "SystemTimeToTzSpecificLocalTime"
api_TerminateProcess% (hProcess%, uExitCode%) : "TerminateProcess"
api_TerminateThread% (hThread%, dwExitCode%) : "TerminateThread"
api_TlsAlloc% () : "TlsAlloc"
api_TlsFree% (dwTlsIndex%) : "TlsFree"
api_TlsGetValue% (dwTlsIndex%) : "TlsGetValue"
api_TlsSetValue% (dwTlsIndex%, lpTlsValue*) : "TlsSetValue"
api_TransactNamedPipe% (hNamedPipe%, lpInBuffer*, nInBufferSize%, lpOutBuffer*, nOutBufferSize%, lpBytesRead%, lpOverlapped*) : "TransactNamedPipe"
api_TransmitCommChar% (nCid%, cChar%) : "TransmitCommChar"
api_UnhandledExceptionFilter% (ExceptionInfo*) : "UnhandledExceptionFilter"
api_UnlockFile% (hFile%, dwFileOffsetLow%, dwFileOffsetHigh%, nNumberOfBytesToUnlockLow%, nNumberOfBytesToUnlockHigh%) : "UnlockFile"
api_UnlockFileEx% (hFile%, dwReserved%, nNumberOfBytesToUnlockLow%, nNumberOfBytesToUnlockHigh%, lpOverlapped*) : "UnlockFileEx"
api_UnmapViewOfFile% (lpBaseAddress*) : "UnmapViewOfFile"
api_UpdateResource% (hUpdate%, lpType$, lpName$, wLanguage%, lpData*, cbData%) : "UpdateResourceA"
api_VerLanguageName% (wLang%, szLang$, nSize%) : "VerLanguageNameA"
api_VirtualAlloc% (lpAddress*, dwSize%, flAllocationType%, flProtect%) : "VirtualAlloc"
api_VirtualFree% (lpAddress*, dwSize%, dwFreeType%) : "VirtualFree"
api_VirtualLock% (lpAddress*, dwSize%) : "VirtualLock"
api_VirtualProtect% (lpAddress*, dwSize%, flNewProtect%, lpflOldProtect%) : "VirtualProtect"
api_VirtualProtectEx% (hProcess%, lpAddress*, dwSize%, flNewProtect%, lpflOldProtect%) : "VirtualProtectEx"
api_VirtualQuery% (lpAddress*, lpBuffer*, dwLength%) : "VirtualQuery"
api_VirtualQueryEx% (hProcess%, lpAddress*, lpBuffer*, dwLength%) : "VirtualQueryEx"
api_VirtualUnlock% (lpAddress*, dwSize%) : "VirtualUnlock"
api_WaitCommEvent% (hFile%, lpEvtMask%, lpOverlapped*) : "WaitCommEvent"
api_WaitForMultipleObjects% (nCount%, lpHandles%, bWaitAll%, dwMilliseconds%) : "WaitForMultipleObjects"
api_WaitForMultipleObjectsEx% (nCount%, lpHandles%, bWaitAll%, dwMilliseconds%, bAlertable%) : "WaitForMultipleObjectsEx"
api_WaitForSingleObject% (hHandle%, dwMilliseconds%) : "WaitForSingleObject"
api_WaitForSingleObjectEx% (hHandle%, dwMilliseconds%, bAlertable%) : "WaitForSingleObjectEx"
api_WaitNamedPipe% (lpNamedPipeName$, nTimeOut%) : "WaitNamedPipeA"
api_WideCharToMultiByte% (CodePage%, dwFlags%, lpWideCharStr$, cchWideChar%, lpMultiByteStr$, cchMultiByte%, lpDefaultChar$, lpUsedDefaultChar%) : "WideCharToMultiByte"
api_WinExec% (lpCmdLine$, nCmdShow%) : "WinExec"
api_WriteConsole% (hConsoleOutput%, lpBuffer*, nNumberOfCharsToWrite%, lpNumberOfCharsWritten%, lpReserved*) : "WriteConsoleA"
api_WriteConsoleOutput% (hConsoleOutput%, lpBuffer*, dwBufferSize*, dwBufferCoord*, lpWriteRegion*) : "WriteConsoleOutputA"
api_WriteConsoleOutputAttribute% (hConsoleOutput%, lpAttribute%, nLength%, dwWriteCoord*, lpNumberOfAttrsWritten%) : "WriteConsoleOutputAttribute"
api_WriteConsoleOutputCharacter% (hConsoleOutput%, lpCharacter$, nLength%, dwWriteCoord*, lpNumberOfCharsWritten%) : "WriteConsoleOutputCharacterA"
api_WriteFile% (hFile%, lpBuffer*, nNumberOfBytesToWrite%, lpNumberOfBytesWritten%, lpOverlapped*) : "WriteFile"
api_WriteFileEx% (hFile%, lpBuffer*, nNumberOfBytesToWrite%, lpOverlapped*, lpCompletionRoutine%) : "WriteFileEx"
api_WritePrivateProfileSection% (lpAppName$, lpString$, lpFileName$) : "WritePrivateProfileSectionA"
api_WritePrivateProfileString% (lpApplicationName$, lpKeyName*, lpString*, lpFileName$) : "WritePrivateProfileStringA"
api_WriteProcessMemory% (hProcess%, lpBaseAddress*, lpBuffer*, nSize%, lpNumberOfBytesWritten%) : "WriteProcessMemory"
api_WriteProfileSection% (lpAppName$, lpString$) : "WriteProfileSectionA"
api_WriteProfileString% (lpszSection$, lpszKeyName$, lpszString$) : "WriteProfileStringA"
api_WriteTapemark% (hDevice%, dwTapemarkType%, dwTapemarkCount%, bimmediate%) : "WriteTapemark"