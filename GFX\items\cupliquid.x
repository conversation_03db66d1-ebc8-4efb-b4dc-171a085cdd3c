xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
<b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}


Frame Scene_Root {


 FrameTransformMatrix {
  1.000000, 0.000000, 0.000000, 0.000000,
  0.000000, 1.000000, 0.000000, 0.000000,
  0.000000, 0.000000, 1.000000, 0.000000,
  0.000000, 0.000000, 0.000000, 1.000000;;
 }

  Frame Cylinder01 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    13;
    1.019538;2.659541;0.000576;,
    0.001946;2.659541;0.000576;,
    0.883207;2.659541;-0.508221;,
    0.510743;2.659541;-0.880685;,
    0.001946;2.659541;-1.017017;,
    -0.506850;2.659541;-0.880685;,
    -0.879315;2.659541;-0.508221;,
    -1.015646;2.659541;0.000576;,
    -0.879315;2.659541;0.509372;,
    -0.506850;2.659541;0.881837;,
    0.001946;2.659541;1.018168;,
    0.510743;2.659541;0.881837;,
    0.883207;2.659541;0.509372;;
    12;
    3;0,2,1;,
    3;2,3,1;,
    3;3,4,1;,
    3;4,5,1;,
    3;5,6,1;,
    3;6,7,1;,
    3;7,8,1;,
    3;8,9,1;,
    3;9,10,1;,
    3;10,11,1;,
    3;11,12,1;,
    3;12,0,1;;

    MeshNormals {
     13;
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;;
     12;
     3;0,2,1;,
     3;2,3,1;,
     3;3,4,1;,
     3;4,5,1;,
     3;5,6,1;,
     3;6,7,1;,
     3;7,8,1;,
     3;8,9,1;,
     3;9,10,1;,
     3;10,11,1;,
     3;11,12,1;,
     3;12,0,1;;
    }

    MeshTextureCoords {
     13;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     13;
     13;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12;
    }

    MeshMaterialList {
     1;
     12;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material DefaultMat {
      0.800000;0.800000;0.800000;0.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
 }
}

AnimationSet AnimationSet0
{
 Animation
 {
  AnimationKey
  {
   4;
   2;
   0; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;,
   -1; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;;
  }
  { Scene_Root }
 }
}

