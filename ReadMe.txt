

***********************************************************************************

**************************    SCP - CONTAINMENT BREACH   **************************

***********************************************************************************

				 www.scpcbgame.com
							


The game is based on the works of the SCP Foundation community (http://www.scp-wiki.net/).
See "Credits.txt" for further details.


This game is licensed under Creative Commons Attribution-ShareAlike 3.0 License.

http://creativecommons.org/licenses/by-sa/3.0/


The source code of the game is available on GitHub: https://github.com/Regalis11/scpcb



**** HOW TO RUN IT **************************************

	Run SCP - Containment Breach.exe in the game folder. 
	Select which resolution you want to use and click the LAUNCH-button.

	SCP-CB uses an engine called Blitz3D, and since it's a fairly outdated one,
	you might run into problems when trying to run it on newer systems.

	If you're getting an error such as "Memory Access Violation" or
	"Unable to set graphics mode" when launching the game, try the following:

	- Run the game as administrator
	- Make sure your graphics card drivers are up-to-date
	- Try different resolutions and running the game in full screen
	- Try running the game in different compatibility modes 


**** DEFAULT CONTROLS ***********************************

	WASD				-	Move.
	Space				-	Manual blink.
	Left Shift			-	Sprint.
	Left Control			-	Crouch.
	Tab				-	Toggle inventory.
	F5				-	Save.
	F3				-	[DATA EXPUNGED]

	Pick up items and use levers/buttons by left-clicking on them.

	Use items by double left-clicking them in the inventory.

	You can drop items by clicking and dragging them out of their inventory slot.

	You can combine certain items by dragging an item into the another inventory slot.









