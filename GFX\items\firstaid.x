xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
<b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}


Frame Scene_Root {


 FrameTransformMatrix {
  1.000000, 0.000000, 0.000000, 0.000000,
  0.000000, 1.000000, 0.000000, 0.000000,
  0.000000, 0.000000, 1.000000, 0.000000,
  0.000000, 0.000000, 0.000000, 1.000000;;
 }

  Frame Mesh01 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    -4.000000;1.175000;2.612500;,
    3.200000;1.175000;2.612500;,
    3.200000;-0.025000;2.612500;,
    -4.000000;-0.025000;2.612500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.773800;0.000000;,
     -0.238100;0.000000;,
     -0.238100;0.089300;,
     -0.773800;0.089300;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D01 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Mesh02 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    -4.000000;-0.025000;-2.587500;,
    3.200000;-0.025000;-2.587500;,
    3.200000;1.175000;-2.587500;,
    -4.000000;1.175000;-2.587500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.773800;0.089300;,
     -0.238100;0.089300;,
     -0.238100;0.000000;,
     -0.773800;0.000000;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D02 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Mesh03 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    3.200000;-0.025000;2.612500;,
    3.200000;-0.025000;-2.587500;,
    -4.000000;-0.025000;-2.587500;,
    -4.000000;-0.025000;2.612500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.238100;0.989600;,
     -0.238100;0.602700;,
     -0.773800;0.602700;,
     -0.773800;0.989600;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D03 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Mesh04 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    -4.000000;1.175000;2.612500;,
    -4.000000;1.175000;-2.587500;,
    3.200000;1.175000;-2.587500;,
    3.200000;1.175000;2.612500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.773800;0.491100;,
     -0.773800;0.104200;,
     -0.238100;0.104200;,
     -0.238100;0.491100;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D04 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Mesh05 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    -4.000000;-0.025000;-2.587500;,
    -4.000000;1.175000;-2.587500;,
    -4.000000;1.175000;2.612500;,
    -4.000000;-0.025000;2.612500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.133900;-0.900300;,
     -0.223200;-0.900300;,
     -0.223200;-0.513400;,
     -0.133900;-0.513400;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D05 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Mesh06 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    4;
    3.200000;-0.025000;2.612500;,
    3.200000;1.175000;2.612500;,
    3.200000;1.175000;-2.587500;,
    3.200000;-0.025000;-2.587500;;
    2;
    3;0,2,1;,
    3;0,3,2;;

    MeshNormals {
     4;
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;;
     2;
     3;0,2,1;,
     3;0,3,2;;
    }

    MeshTextureCoords {
     4;
     -0.788700;0.491100;,
     -0.878000;0.491100;,
     -0.878000;0.104200;,
     -0.788700;0.104200;;
    }

    VertexDuplicationIndices {
     4;
     4;
     0,
     1,
     2,
     3;
    }

    MeshMaterialList {
     1;
     2;
     0,
     0;

     Material MatD3D06 {
      1.000000;1.000000;1.000000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "firstaidkit.jpg";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
 }
}

AnimationSet AnimationSet0
{
 Animation
 {
  AnimationKey
  {
   4;
   2;
   0; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;,
   -1; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;;
  }
  { Scene_Root }
 }
}

