xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
 <b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}

template AnimTicksPerSecond {
 <9e415a43-7ba6-4a73-8743-b73d47e88476>
 DWORD AnimTicksPerSecond;
}


AnimTicksPerSecond {
 24;
}

Frame Root {
 

 FrameTransformMatrix {
  1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000;;
 }

 Frame classname_mesh {
  

  FrameTransformMatrix {
   1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,-96.000000,0.000000,1.000000;;
  }

  Mesh classname_mesh {
   163;
   -0.044238;95.615326;-37.264744;,
   -16.187635;95.615341;-33.580059;,
   -0.044238;121.604935;-0.057888;,
   -29.133820;95.615311;-23.255903;,
   -36.318138;95.615326;-8.337115;,
   -36.318138;95.615341;8.221498;,
   -29.133820;95.615326;23.140354;,
   -16.187635;95.615326;33.464401;,
   -0.044238;95.615326;37.148899;,
   16.099161;95.615341;-33.580059;,
   29.045343;95.615311;-23.255903;,
   36.229660;95.615326;-8.337115;,
   36.229660;95.615341;8.221498;,
   29.045343;95.615326;23.140354;,
   16.099161;95.615326;33.464401;,
   7.601841;133.627029;-10.581708;,
   12.327341;133.627029;-4.077560;,
   -0.044238;133.627014;-13.065981;,
   12.327341;133.627029;3.961943;,
   14.384202;113.941086;-4.746058;,
   12.327341;133.627029;-4.077560;,
   8.872982;113.941055;-12.331547;,
   7.601841;133.627029;-10.581708;,
   14.384202;113.941086;4.630135;,
   12.327341;133.627029;3.961943;,
   8.872982;113.941071;12.215722;,
   7.601841;133.626984;10.465832;,
   -0.044238;113.941032;15.113129;,
   -0.044238;133.626953;12.950205;,
   -14.472678;113.941086;-4.746058;,
   -8.961457;113.941055;-12.331547;,
   -12.415817;133.627029;-4.077560;,
   -7.690317;133.627029;-10.581708;,
   -14.472678;113.941086;4.630135;,
   -12.415817;133.627029;3.961943;,
   -8.961457;113.941071;12.215722;,
   -7.690317;133.626984;10.465832;,
   -0.044238;113.941071;-15.228964;,
   -0.044238;133.627014;-13.065981;,
   -0.044238;113.941071;-15.228964;,
   -0.044238;133.627014;-13.065981;,
   -30.022758;104.924782;-6.900413;,
   -24.085136;104.924759;-19.229832;,
   -14.472678;113.941086;-4.746058;,
   -8.961457;113.941055;-12.331547;,
   -13.385878;104.924751;-27.762253;,
   -16.187635;95.615341;-33.580059;,
   -0.044238;104.924789;-30.807402;,
   -0.044238;95.615326;-37.264744;,
   -29.133820;95.615311;-23.255903;,
   -36.318138;95.615326;-8.337115;,
   -30.022758;104.924774;6.784489;,
   -14.472678;113.941086;4.630135;,
   -36.318138;95.615341;8.221498;,
   -24.085136;104.924774;19.113897;,
   -29.133820;95.615326;23.140354;,
   -13.385878;104.924789;27.646378;,
   -16.187635;95.615326;33.464401;,
   -0.044238;104.924782;30.691586;,
   -0.044238;113.941032;15.113129;,
   -8.961457;113.941071;12.215722;,
   23.996662;104.924774;19.113897;,
   29.045343;95.615326;23.140354;,
   13.297401;104.924789;27.646378;,
   16.099161;95.615326;33.464401;,
   -0.044238;95.615326;37.148899;,
   -0.044238;113.941071;-15.228964;,
   -0.044238;104.924789;-30.807402;,
   8.872982;113.941055;-12.331547;,
   13.297401;104.924751;-27.762253;,
   -0.044238;95.615326;-37.264744;,
   16.099161;95.615341;-33.580059;,
   23.996662;104.924759;-19.229832;,
   29.045343;95.615311;-23.255903;,
   29.934284;104.924782;-6.900413;,
   36.229660;95.615326;-8.337115;,
   8.872982;113.941071;12.215722;,
   14.384202;113.941086;4.630135;,
   29.934284;104.924774;6.784489;,
   36.229660;95.615341;8.221498;,
   -0.684488;228.851089;0.086566;,
   -0.044238;228.850571;-0.553556;,
   -0.044238;228.850571;0.726777;,
   0.596012;228.851089;0.086566;,
   -0.684488;228.851089;0.086566;,
   -0.044238;228.850571;0.726777;,
   -0.684488;132.815506;0.086585;,
   -0.044238;132.815506;0.726787;,
   -0.044238;228.850571;-0.553556;,
   -0.044238;132.815002;-0.553545;,
   -0.044238;228.850571;0.726777;,
   0.596012;228.851089;0.086566;,
   -0.044238;132.815506;0.726787;,
   0.596012;132.815506;0.086585;,
   -0.044238;110.030228;10.766690;,
   -0.044238;118.359764;5.710941;,
   5.367922;110.030243;9.316351;,
   4.951662;118.359818;2.826547;,
   9.330062;110.030289;5.354263;,
   10.780339;110.030251;-0.057907;,
   4.951662;118.359756;-2.942421;,
   9.330062;110.030273;-5.470068;,
   5.367922;110.030228;-9.432244;,
   -0.044238;118.359756;-5.826806;,
   -0.044238;110.030281;-10.882466;,
   4.642782;107.324051;8.060527;,
   -0.044238;107.324066;9.316430;,
   8.074002;107.324028;4.629380;,
   9.330062;107.324051;-0.057928;,
   8.074002;107.324051;-4.744948;,
   4.642782;107.324081;-8.176134;,
   -0.044238;107.323997;-9.432284;,
   -0.044238;110.030281;-10.882466;,
   -0.044238;118.359756;-5.826806;,
   -5.456399;110.030228;-9.432244;,
   -5.040139;118.359756;-2.942421;,
   -9.418539;110.030273;-5.470068;,
   -10.868816;110.030251;-0.057907;,
   -5.040139;118.359818;2.826547;,
   -9.418539;110.030289;5.354263;,
   -5.456399;110.030243;9.316351;,
   -0.044238;118.359764;5.710941;,
   -0.044238;110.030228;10.766690;,
   -4.731257;107.324081;-8.176134;,
   -0.044238;107.323997;-9.432284;,
   -8.162478;107.324051;-4.744948;,
   -9.418539;107.324051;-0.057928;,
   -8.162478;107.324028;4.629380;,
   -4.731257;107.324051;8.060527;,
   -0.044238;107.324066;9.316430;,
   4.642782;107.324051;8.060527;,
   2.661842;105.343170;4.629361;,
   -0.044238;107.324066;9.316430;,
   -0.044238;105.343094;5.354302;,
   8.074002;107.324028;4.629380;,
   4.642782;105.343132;2.648137;,
   9.330062;107.324051;-0.057928;,
   5.367922;105.343155;-0.057868;,
   8.074002;107.324051;-4.744948;,
   4.642782;105.343163;-2.764091;,
   4.642782;107.324081;-8.176134;,
   2.661842;105.343132;-4.744957;,
   -0.044238;105.343132;-5.470049;,
   -2.750318;105.343132;-4.744957;,
   -4.731257;105.343163;-2.764091;,
   -5.456399;105.343155;-0.057868;,
   -4.731257;105.343132;2.648137;,
   -2.750318;105.343170;4.629361;,
   -0.044238;104.617981;-0.057888;,
   -0.044238;113.941071;-15.228964;,
   -24.085136;104.924774;19.113897;,
   13.297401;104.924789;27.646378;,
   14.384202;113.941086;-4.746058;,
   -7.690317;133.626984;10.465832;,
   -12.415817;133.627029;3.961943;,
   -0.044238;133.626953;12.950205;,
   -12.415817;133.627029;-4.077560;,
   7.601841;133.626984;10.465832;,
   -7.690317;133.627029;-10.581708;,
   -0.684488;228.851089;0.086566;,
   -0.684488;132.815506;0.086585;,
   0.596012;228.851089;0.086566;,
   0.596012;132.815506;0.086585;;
   186;
   3;0,2,1;,
   3;1,2,3;,
   3;3,2,4;,
   3;4,2,5;,
   3;5,2,6;,
   3;6,2,7;,
   3;7,2,8;,
   3;2,0,9;,
   3;2,9,10;,
   3;2,10,11;,
   3;2,11,12;,
   3;2,12,13;,
   3;2,13,14;,
   3;2,14,8;,
   3;15,17,16;,
   3;17,18,16;,
   3;19,21,20;,
   3;21,22,20;,
   3;23,19,24;,
   3;19,20,24;,
   3;25,23,26;,
   3;23,24,26;,
   3;27,25,28;,
   3;25,26,28;,
   3;29,31,30;,
   3;31,32,30;,
   3;33,34,29;,
   3;34,31,29;,
   3;35,36,33;,
   3;36,34,33;,
   3;27,28,35;,
   3;28,36,35;,
   3;21,37,22;,
   3;37,38,22;,
   3;30,32,39;,
   3;32,40,39;,
   3;41,43,42;,
   3;43,44,42;,
   3;45,47,46;,
   3;47,48,46;,
   3;42,45,49;,
   3;45,46,49;,
   3;41,42,50;,
   3;42,49,50;,
   3;51,52,41;,
   3;52,43,41;,
   3;51,41,53;,
   3;41,50,53;,
   3;54,51,55;,
   3;51,53,55;,
   3;56,54,57;,
   3;54,55,57;,
   3;58,59,56;,
   3;59,60,56;,
   3;61,63,62;,
   3;63,64,62;,
   3;63,58,64;,
   3;58,65,64;,
   3;58,56,65;,
   3;56,57,65;,
   3;66,68,67;,
   3;68,69,67;,
   3;67,69,70;,
   3;69,71,70;,
   3;69,72,71;,
   3;72,73,71;,
   3;72,74,73;,
   3;74,75,73;,
   3;76,61,77;,
   3;61,78,77;,
   3;74,78,75;,
   3;78,79,75;,
   3;78,61,79;,
   3;61,62,79;,
   3;80,82,81;,
   3;82,83,81;,
   3;159,160,85;,
   3;160,87,85;,
   3;88,89,84;,
   3;89,86,84;,
   3;90,92,161;,
   3;92,162,161;,
   3;91,93,88;,
   3;93,89,88;,
   3;94,96,95;,
   3;96,97,95;,
   3;96,98,97;,
   3;98,99,97;,
   3;99,100,97;,
   3;99,101,100;,
   3;101,102,100;,
   3;102,103,100;,
   3;102,104,103;,
   3;96,94,105;,
   3;94,106,105;,
   3;98,96,107;,
   3;96,105,107;,
   3;99,98,108;,
   3;98,107,108;,
   3;101,99,109;,
   3;99,108,109;,
   3;102,101,110;,
   3;101,109,110;,
   3;104,102,111;,
   3;102,110,111;,
   3;112,114,113;,
   3;114,115,113;,
   3;114,116,115;,
   3;116,117,115;,
   3;117,118,115;,
   3;117,119,118;,
   3;119,120,118;,
   3;120,121,118;,
   3;120,122,121;,
   3;114,112,123;,
   3;112,124,123;,
   3;116,114,125;,
   3;114,123,125;,
   3;117,116,126;,
   3;116,125,126;,
   3;119,117,127;,
   3;117,126,127;,
   3;120,119,128;,
   3;119,127,128;,
   3;122,120,129;,
   3;120,128,129;,
   3;130,132,131;,
   3;132,133,131;,
   3;134,130,135;,
   3;130,131,135;,
   3;136,134,137;,
   3;134,135,137;,
   3;138,136,139;,
   3;136,137,139;,
   3;140,138,141;,
   3;138,139,141;,
   3;124,140,142;,
   3;140,141,142;,
   3;123,124,143;,
   3;124,142,143;,
   3;125,123,144;,
   3;123,143,144;,
   3;126,125,145;,
   3;125,144,145;,
   3;127,126,146;,
   3;126,145,146;,
   3;128,127,147;,
   3;127,146,147;,
   3;132,128,133;,
   3;128,147,133;,
   3;131,133,148;,
   3;135,131,148;,
   3;137,135,148;,
   3;139,137,148;,
   3;141,139,148;,
   3;142,141,148;,
   3;143,142,148;,
   3;144,143,148;,
   3;145,144,148;,
   3;146,145,148;,
   3;147,146,148;,
   3;133,147,148;,
   3;45,42,44;,
   3;149,47,44;,
   3;47,45,44;,
   3;56,60,54;,
   3;60,150,54;,
   3;61,76,63;,
   3;76,151,63;,
   3;152,77,74;,
   3;77,78,74;,
   3;74,72,152;,
   3;72,68,152;,
   3;72,69,68;,
   3;153,155,154;,
   3;155,156,154;,
   3;155,157,156;,
   3;157,158,156;,
   3;157,18,158;,
   3;18,17,158;,
   3;63,151,58;,
   3;151,59,58;,
   3;59,151,76;,
   3;54,150,51;,
   3;150,52,51;,
   3;52,150,60;;

   MeshNormals {
    163;
    0.000000;-0.819803;0.572646;,
    0.248462;-0.819803;0.515936;,
    0.000000;-1.000000;-0.000000;,
    0.447714;-0.819803;0.357037;,
    0.558290;-0.819802;0.127424;,
    0.558290;-0.819802;-0.127422;,
    0.447712;-0.819804;-0.357037;,
    0.248457;-0.819803;-0.515938;,
    0.000000;-0.819802;-0.572647;,
    -0.248462;-0.819802;0.515936;,
    -0.447714;-0.819803;0.357037;,
    -0.558290;-0.819802;0.127424;,
    -0.558290;-0.819802;-0.127422;,
    -0.447712;-0.819804;-0.357037;,
    -0.248457;-0.819803;-0.515938;,
    -0.000003;1.000000;0.000002;,
    -0.000002;1.000000;0.000001;,
    -0.000001;1.000000;-0.000000;,
    -0.000001;1.000000;0.000001;,
    0.973014;0.108578;-0.203604;,
    0.906860;0.108579;-0.407205;,
    0.667511;0.108581;-0.736640;,
    0.494314;0.108584;-0.862475;,
    0.906858;0.108576;0.407209;,
    0.973014;0.108575;0.203608;,
    0.494317;0.108584;0.862473;,
    0.667507;0.108579;0.736644;,
    -0.000000;0.109214;0.994018;,
    -0.000000;0.109214;0.994018;,
    -0.973014;0.108578;-0.203604;,
    -0.667511;0.108581;-0.736640;,
    -0.906860;0.108579;-0.407205;,
    -0.494315;0.108584;-0.862475;,
    -0.906858;0.108576;0.407210;,
    -0.973014;0.108575;0.203608;,
    -0.494317;0.108584;0.862473;,
    -0.667507;0.108579;0.736644;,
    0.307340;0.103928;-0.945907;,
    0.307333;0.103931;-0.945909;,
    -0.307340;0.103928;-0.945907;,
    -0.307333;0.103931;-0.945909;,
    -0.668555;0.736804;-0.100768;,
    -0.550527;0.736150;-0.393705;,
    -0.456862;0.872934;-0.171066;,
    -0.248919;0.872664;-0.420115;,
    -0.342271;0.706451;-0.619498;,
    -0.299607;0.568835;-0.765939;,
    -0.163728;0.729934;-0.663619;,
    -0.184332;0.560179;-0.807602;,
    -0.602264;0.568839;-0.560089;,
    -0.785638;0.568833;-0.243313;,
    -0.642875;0.665132;0.379884;,
    -0.469508;0.873542;0.128400;,
    -0.813406;0.568833;0.121654;,
    -0.402243;0.484794;0.776644;,
    -0.680059;0.568850;0.462526;,
    -0.254415;0.736339;0.626960;,
    -0.412027;0.568848;0.711790;,
    0.033043;0.609575;0.792039;,
    -0.000000;0.873812;0.486265;,
    -0.248586;0.745602;0.618290;,
    0.550524;0.736154;0.393700;,
    0.602257;0.568852;0.560083;,
    0.285552;0.469641;0.835402;,
    0.299601;0.568841;0.765937;,
    -0.062393;0.568837;0.820080;,
    0.160648;0.854253;-0.494413;,
    0.154836;0.779502;-0.606961;,
    0.248919;0.872664;-0.420115;,
    0.273417;0.706524;-0.652738;,
    0.184332;0.560179;-0.807602;,
    0.412034;0.568836;-0.711796;,
    0.510979;0.736321;-0.443545;,
    0.680066;0.568837;-0.462531;,
    0.655168;0.737684;-0.163023;,
    0.813408;0.568830;-0.121655;,
    0.248586;0.745602;0.618290;,
    0.469508;0.873542;0.128400;,
    0.698614;0.705199;0.120970;,
    0.785635;0.568839;0.243310;,
    0.000810;1.000000;0.000000;,
    0.000000;1.000000;0.000000;,
    0.000000;1.000000;0.000000;,
    -0.000810;1.000000;0.000000;,
    -0.707038;-0.000000;-0.707175;,
    -0.707083;0.000000;0.707131;,
    -0.707041;-0.000000;-0.707173;,
    -0.707080;0.000000;0.707133;,
    0.316173;-0.000000;-0.948702;,
    -0.316172;-0.000000;-0.948702;,
    0.707085;0.000000;0.707128;,
    0.707036;-0.000000;-0.707178;,
    0.707083;0.000000;0.707131;,
    0.707038;-0.000000;-0.707175;,
    0.255715;-0.154852;0.954270;,
    0.318662;0.543262;0.776738;,
    0.538348;0.040605;0.841744;,
    0.709667;0.573142;0.409733;,
    0.888492;-0.086238;0.450716;,
    0.998146;0.040588;-0.045354;,
    0.709667;0.573141;-0.409733;,
    0.834565;-0.086262;-0.544115;,
    0.459814;0.040577;-0.887088;,
    0.318659;0.543262;-0.776739;,
    0.258736;0.026317;-0.965590;,
    0.371388;-0.470877;0.800216;,
    0.229851;-0.459709;0.857809;,
    0.721731;-0.470874;0.507328;,
    0.878690;-0.470897;0.078482;,
    0.800198;-0.470904;-0.371393;,
    0.507320;-0.470924;-0.721704;,
    0.229876;-0.459714;-0.857800;,
    -0.255721;-0.154866;-0.954266;,
    -0.318659;0.543262;-0.776739;,
    -0.538347;0.040569;-0.841746;,
    -0.709667;0.573141;-0.409733;,
    -0.888492;-0.086254;-0.450714;,
    -0.998146;0.040592;0.045349;,
    -0.709667;0.573142;0.409733;,
    -0.834563;-0.086231;0.544122;,
    -0.459807;0.040598;0.887090;,
    -0.318662;0.543262;0.776737;,
    -0.258755;0.026300;0.965585;,
    -0.330236;-0.719123;-0.611396;,
    -0.091070;-0.815453;-0.571614;,
    -0.591670;-0.719133;-0.364383;,
    -0.694592;-0.719133;-0.019726;,
    -0.611401;-0.719123;0.330230;,
    -0.364383;-0.719110;0.591697;,
    -0.229865;-0.459689;0.857816;,
    0.257983;-0.893726;0.367014;,
    0.141592;-0.945367;0.293655;,
    0.039910;-0.893723;0.446840;,
    -0.024213;-0.945368;0.325105;,
    0.406915;-0.893730;0.188856;,
    0.269443;-0.945368;0.183519;,
    0.446828;-0.893729;-0.039914;,
    0.325100;-0.945370;0.024211;,
    0.367019;-0.893721;-0.257993;,
    0.293655;-0.945367;-0.141588;,
    0.188870;-0.893722;-0.406928;,
    0.183526;-0.945365;-0.269450;,
    0.024222;-0.945370;-0.325097;,
    -0.141595;-0.945368;-0.293651;,
    -0.269448;-0.945365;-0.183527;,
    -0.325102;-0.945369;-0.024218;,
    -0.293652;-0.945370;0.141579;,
    -0.183524;-0.945369;0.269438;,
    -0.000000;-1.000000;-0.000001;,
    -0.160648;0.854253;-0.494413;,
    -0.270720;0.546741;0.792329;,
    0.076973;0.502995;0.860855;,
    0.456862;0.872934;-0.171066;,
    0.000002;1.000000;0.000005;,
    0.000004;1.000000;0.000003;,
    0.000002;1.000000;0.000004;,
    0.000002;1.000000;0.000002;,
    -0.000001;1.000000;0.000004;,
    -0.000000;1.000000;0.000001;,
    -0.707085;0.000000;0.707128;,
    -0.707083;0.000000;0.707131;,
    0.707083;0.000000;0.707131;,
    0.707080;0.000000;0.707133;;
    186;
    3;0,2,1;,
    3;1,2,3;,
    3;3,2,4;,
    3;4,2,5;,
    3;5,2,6;,
    3;6,2,7;,
    3;7,2,8;,
    3;2,0,9;,
    3;2,9,10;,
    3;2,10,11;,
    3;2,11,12;,
    3;2,12,13;,
    3;2,13,14;,
    3;2,14,8;,
    3;15,17,16;,
    3;17,18,16;,
    3;19,21,20;,
    3;21,22,20;,
    3;23,19,24;,
    3;19,20,24;,
    3;25,23,26;,
    3;23,24,26;,
    3;27,25,28;,
    3;25,26,28;,
    3;29,31,30;,
    3;31,32,30;,
    3;33,34,29;,
    3;34,31,29;,
    3;35,36,33;,
    3;36,34,33;,
    3;27,28,35;,
    3;28,36,35;,
    3;21,37,22;,
    3;37,38,22;,
    3;30,32,39;,
    3;32,40,39;,
    3;41,43,42;,
    3;43,44,42;,
    3;45,47,46;,
    3;47,48,46;,
    3;42,45,49;,
    3;45,46,49;,
    3;41,42,50;,
    3;42,49,50;,
    3;51,52,41;,
    3;52,43,41;,
    3;51,41,53;,
    3;41,50,53;,
    3;54,51,55;,
    3;51,53,55;,
    3;56,54,57;,
    3;54,55,57;,
    3;58,59,56;,
    3;59,60,56;,
    3;61,63,62;,
    3;63,64,62;,
    3;63,58,64;,
    3;58,65,64;,
    3;58,56,65;,
    3;56,57,65;,
    3;66,68,67;,
    3;68,69,67;,
    3;67,69,70;,
    3;69,71,70;,
    3;69,72,71;,
    3;72,73,71;,
    3;72,74,73;,
    3;74,75,73;,
    3;76,61,77;,
    3;61,78,77;,
    3;74,78,75;,
    3;78,79,75;,
    3;78,61,79;,
    3;61,62,79;,
    3;80,82,81;,
    3;82,83,81;,
    3;159,160,85;,
    3;160,87,85;,
    3;88,89,84;,
    3;89,86,84;,
    3;90,92,161;,
    3;92,162,161;,
    3;91,93,88;,
    3;93,89,88;,
    3;94,96,95;,
    3;96,97,95;,
    3;96,98,97;,
    3;98,99,97;,
    3;99,100,97;,
    3;99,101,100;,
    3;101,102,100;,
    3;102,103,100;,
    3;102,104,103;,
    3;96,94,105;,
    3;94,106,105;,
    3;98,96,107;,
    3;96,105,107;,
    3;99,98,108;,
    3;98,107,108;,
    3;101,99,109;,
    3;99,108,109;,
    3;102,101,110;,
    3;101,109,110;,
    3;104,102,111;,
    3;102,110,111;,
    3;112,114,113;,
    3;114,115,113;,
    3;114,116,115;,
    3;116,117,115;,
    3;117,118,115;,
    3;117,119,118;,
    3;119,120,118;,
    3;120,121,118;,
    3;120,122,121;,
    3;114,112,123;,
    3;112,124,123;,
    3;116,114,125;,
    3;114,123,125;,
    3;117,116,126;,
    3;116,125,126;,
    3;119,117,127;,
    3;117,126,127;,
    3;120,119,128;,
    3;119,127,128;,
    3;122,120,129;,
    3;120,128,129;,
    3;130,132,131;,
    3;132,133,131;,
    3;134,130,135;,
    3;130,131,135;,
    3;136,134,137;,
    3;134,135,137;,
    3;138,136,139;,
    3;136,137,139;,
    3;140,138,141;,
    3;138,139,141;,
    3;124,140,142;,
    3;140,141,142;,
    3;123,124,143;,
    3;124,142,143;,
    3;125,123,144;,
    3;123,143,144;,
    3;126,125,145;,
    3;125,144,145;,
    3;127,126,146;,
    3;126,145,146;,
    3;128,127,147;,
    3;127,146,147;,
    3;132,128,133;,
    3;128,147,133;,
    3;131,133,148;,
    3;135,131,148;,
    3;137,135,148;,
    3;139,137,148;,
    3;141,139,148;,
    3;142,141,148;,
    3;143,142,148;,
    3;144,143,148;,
    3;145,144,148;,
    3;146,145,148;,
    3;147,146,148;,
    3;133,147,148;,
    3;45,42,44;,
    3;149,47,44;,
    3;47,45,44;,
    3;56,60,54;,
    3;60,150,54;,
    3;61,76,63;,
    3;76,151,63;,
    3;152,77,74;,
    3;77,78,74;,
    3;74,72,152;,
    3;72,68,152;,
    3;72,69,68;,
    3;153,155,154;,
    3;155,156,154;,
    3;155,157,156;,
    3;157,158,156;,
    3;157,18,158;,
    3;18,17,158;,
    3;63,151,58;,
    3;151,59,58;,
    3;59,151,76;,
    3;54,150,51;,
    3;150,52,51;,
    3;52,150,60;;
   }

   MeshTextureCoords {
    163;
    0.395100;0.561100;,
    0.428700;0.634100;,
    0.700600;0.474400;,
    0.475300;0.698200;,
    0.543100;0.746500;,
    0.616000;0.779500;,
    0.699200;0.790000;,
    0.778700;0.780700;,
    0.854000;0.751600;,
    0.428700;0.634100;,
    0.475300;0.698200;,
    0.543100;0.746500;,
    0.616000;0.779500;,
    0.699200;0.790000;,
    0.778700;0.780700;,
    0.685400;0.111800;,
    0.651600;0.087300;,
    0.698300;0.151500;,
    0.610000;0.087300;,
    0.496200;0.992100;,
    0.517800;0.791400;,
    0.451900;0.986700;,
    0.478200;0.785500;,
    0.542900;0.996100;,
    0.555600;0.794400;,
    0.589000;0.996700;,
    0.595500;0.796900;,
    0.635100;0.997500;,
    0.635400;0.796900;,
    0.772800;0.992900;,
    0.819700;0.986900;,
    0.752200;0.790900;,
    0.792100;0.785500;,
    0.728200;0.996700;,
    0.712800;0.794700;,
    0.680700;0.997700;,
    0.674000;0.796600;,
    0.407100;0.980700;,
    0.439600;0.780400;,
    0.857500;0.980200;,
    0.830500;0.780400;,
    0.340700;0.275500;,
    0.305300;0.220300;,
    0.432700;0.199100;,
    0.408000;0.149000;,
    0.285100;0.153500;,
    0.193500;0.173600;,
    0.284600;0.087800;,
    0.191900;0.094100;,
    0.220200;0.254400;,
    0.266000;0.328700;,
    0.379000;0.323900;,
    0.466900;0.240300;,
    0.318200;0.397500;,
    0.436100;0.363500;,
    0.388200;0.446400;,
    0.496400;0.383100;,
    0.475400;0.477100;,
    0.559600;0.392300;,
    0.569800;0.272100;,
    0.519100;0.263800;,
    0.689800;0.366800;,
    0.734600;0.456300;,
    0.629500;0.387600;,
    0.644200;0.485200;,
    0.559600;0.490900;,
    0.709700;0.109700;,
    0.827000;0.085600;,
    0.707000;0.153700;,
    0.826200;0.147800;,
    0.925700;0.087000;,
    0.923700;0.163600;,
    0.812900;0.211300;,
    0.906200;0.245000;,
    0.787200;0.272600;,
    0.868600;0.323600;,
    0.617100;0.266900;,
    0.655700;0.243000;,
    0.744600;0.325300;,
    0.813400;0.395900;,
    0.847600;0.684600;,
    0.857900;0.684600;,
    0.847600;0.694800;,
    0.857900;0.694800;,
    0.837200;0.684600;,
    0.826800;0.684600;,
    0.837200;0.644500;,
    0.826800;0.644500;,
    0.847600;0.684600;,
    0.847600;0.644500;,
    0.868300;0.684600;,
    0.857900;0.684600;,
    0.868300;0.644500;,
    0.857900;0.644500;,
    0.300300;0.752100;,
    0.333300;0.664800;,
    0.326100;0.744500;,
    0.365900;0.654700;,
    0.353700;0.739000;,
    0.381500;0.737800;,
    0.396500;0.655900;,
    0.408400;0.739900;,
    0.436700;0.745800;,
    0.428300;0.666500;,
    0.463000;0.755900;,
    0.338700;0.790800;,
    0.332200;0.815400;,
    0.356700;0.772800;,
    0.381300;0.765700;,
    0.406300;0.773300;,
    0.423800;0.790800;,
    0.441800;0.771700;,
    0.300300;0.752100;,
    0.333300;0.664800;,
    0.326100;0.744500;,
    0.365900;0.654700;,
    0.353700;0.739000;,
    0.381500;0.737800;,
    0.396500;0.655900;,
    0.408400;0.739900;,
    0.436700;0.745800;,
    0.428300;0.666500;,
    0.463000;0.755900;,
    0.338700;0.790800;,
    0.332200;0.815400;,
    0.356700;0.772800;,
    0.381300;0.765700;,
    0.406300;0.773300;,
    0.423800;0.790800;,
    0.441800;0.771700;,
    0.423800;0.839900;,
    0.405800;0.829200;,
    0.430400;0.815400;,
    0.409600;0.815000;,
    0.405800;0.857900;,
    0.395500;0.839600;,
    0.381300;0.864500;,
    0.381300;0.843400;,
    0.356700;0.857900;,
    0.367100;0.839600;,
    0.338700;0.839900;,
    0.356700;0.829200;,
    0.352900;0.815000;,
    0.356700;0.800800;,
    0.367100;0.790400;,
    0.381300;0.786600;,
    0.395500;0.790400;,
    0.405800;0.800800;,
    0.381300;0.814900;,
    0.404900;0.103100;,
    0.436100;0.363500;,
    0.629500;0.387600;,
    0.689400;0.204000;,
    0.576200;0.191100;,
    0.610000;0.215600;,
    0.563300;0.151500;,
    0.651600;0.215600;,
    0.576200;0.111800;,
    0.685400;0.191100;,
    0.837200;0.684600;,
    0.837200;0.644500;,
    0.857900;0.684600;,
    0.857900;0.644500;;
   }

   VertexDuplicationIndices {
    163;
    159;
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    84,
    86,
    91,
    93;
   }

   MeshMaterialList {
    1;
    186;
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0;

    Material _2_office_lamps_dif {
     1.000000;1.000000;1.000000;1.000000;;
     50.000000;
     0.600000;0.600000;0.600000;;
     0.000000;0.000000;0.000000;;

     TextureFilename {
      "2_office_lamps_dif.jpg";
     }
    }
   }

   XSkinMeshHeader {
    1;
    1;
    1;
   }

   SkinWeights {
    "classname_mesh";
    163;
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162;
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000,
    1.000000;
    1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,96.000000,0.000000,1.000000;;
   }
  }
 }
}

AnimationSet Animation {
 
}