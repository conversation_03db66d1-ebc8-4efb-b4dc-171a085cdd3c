[alarm]
descr=The start event. Should always be present in all maps.
room1=start

[008]
descr=The event in SCP-008's chamber where SCP-173 breaks the window. Should always be present in all maps.
room1=008

[914]
descr=SCP-914's mechanism event. Required for SCP-914 to function.
room1=914

[coffin]
descr=SCP-895's chamber event.
room1=coffin

[coffin106]
descr=SCP-895's chamber event + SCP-106 spawn near the coffin.
room1=coffin106

[gateaentrance]
descr=Gate A entrance event. Should always be present in all maps.
room1=gateaentrance

[exit1]
descr=Gate B event. Should always be present in all maps.
room1=exit1

[endroom106]
descr=An event where SCP-106 takes <PERSON><PERSON> into the Pocket Dimension.
room1=endroom

[medibay]
descr=Spawns an infected surgeon inside the room.
room1=medibay

[checkpoint]
descr=CheckPoint room event.
room1=checkpoint1
room2=checkpoint2

[lockroom173]
descr=Spawns SCP-173 inside the room.
room1=lockroom

[lockroom096]
descr=Spawns SCP-096 inside the room.
room1=lockroom2

[testroom]
descr=Opens up the gas valves and plays a sound through the intercom once the player enters the lower level of the room.
room1=testroom

[tunnel2]
descr=An event where the lights go out and SCP-173 spawns in front of the player.
room1=tunnel2

[tunnel2smoke]
descr=Opens up the gas valves in the room.
room1=tunnel2

[096spawn]
descr=Spawns SCP-096 inside the room.
room1=tunnel2
room2=room2pipes
room3=room2pit
room4=room3pit
room5=room3tunnel
room6=tunnel
room7=room4tunnels
room8=room4pit
room9=room3z2

[roompj]
descr=Spawns SCP-372 once the player enters the room.
room1=roompj

[room012]
descr=Required for SCP-012 to function.
room1=room012

[room035]
descr=Spawns a scientist possessed by SCP-035 inside the room.
room1=room035

[room049]
descr=Spawns SCP-049 and updates the levers and elevators within the room.
room1=room049

[room079]
descr=Required for SCP-079 to interact with the player. Should be present in all maps.
room1=room079

[room106]
descr=Required for SCP-106's recall protocol to function.
room1=room106

[room205]
descr=Required for SCP-205 to function.
room1=room205

[room966]
descr=Spawns multiple SCP-966 instances inside the room.
room1=room966

[room1123]
descr=Required for SCP-1123 to function.
room1=room1123

[room2trick]
descr=Secretly turns the player around by 180 degrees when they reach the middle of the hallway.
room1=room2
room2=room2_3

[1048a]
descr=Spawns SCP-1048-A inside the room.
room1=room2
room2=room2_2
room3=room2_3

[room2fan]
descr=Activates the fan in the room.
room1=room2_2

[room2cafeteria]
descr=Required for SCP-294 to function.
room1=room2cafeteria

[room2ccont]
descr=Required for the levers inside the room to function. Should be present in all maps.
room1=room2ccont

[room2closets]
descr=An event where a scientist and a janitor get killed by SCP-173.
room1=room2closets

[room2elevator]
descr=An event where a guard enters the elevator in the room.
room1=room2elevator

[room2elevator2]
descr=Spawns a dead janitor inside the room.
room1=room2elevator

[room2nuke]
descr=Updates the levers inside the room. Should be present in all maps.
room1=room2nuke

[room2offices2]
descr=Spawns an anomalous duck inside the room.
room1=room2offices2

[room2offices3]
descr=An event where a door closes behind the player.
room1=room2offices3

[room2pipes106]
descr=An event where SCP-106 appears from a wall.
room1=room2pipes

[room2pit]
descr=Spawns SCP-173 inside the room.
room1=room2pit
room2=room2_4

[room2pit106]
descr=Spawns SCP-016 at the lower level of the room.
room1=room2pit

[room2poffices2]
descr=Plays some sound effects when the player enters Dr L's office.
room1=room2poffices2

[room2servers]
descr=An event where SCP-096 kills a guard.
room1=room2servers

[room2storage]
descr=Required for the endless hallway to function.
room1=room2storage

[room2tesla]
descr=Activates the tesla gate in the room.
room1=room2tesla
room2=room2tesla_lcz
room3=room2tesla_hcz

[testroom173]
descr=An event where SCP-173 breaks through the window in the room.
room1=room2testroom2

[toiletguard]
descr=An event where a guard commits suicide.
room1=room2toilets

[buttghost]
descr=Spawns the butt ghost.
room1=room2toilets

[room2tunnel]
descr=Generates and updates the maintenance tunnels.
room1=room2tunnel

[room2doors173]
descr=Spawns SCP-173 inside the room.
room1=room2doors

[room3door]
descr=Closes the doors behind the player.
room1=room3
room2=room3tunnel

[room3servers]
descr=Spawns SCP-173 inside the room.
room1=room3servers
room2=room3servers2

[room3storage]
descr=Spawns SCP-939 instances inside the room and updates the elevators.
room1=room3storage

[room3pitduck]
descr=Spawns a saxophone-playing anomalous duck inside the room.
room1=room3pit

[room3pit1048]
descr=An event where SCP-1048 hands over a drawing to the player.
room1=room3pit

[room3tunnel]
descr=Eventroom3tunnel
room1=room3tunnel

[106victim]
descr=Event where dead scientist falls down from the ceiling.
room1=room3
room2=room3_2

[106sinkhole]
descr=Spawns a sinkhole to the Pocket Dimension on the floor.
room1=room3
room2=room3_2
room3=room4

[room4]
descr=May cause SCP-049 to appear on the walkway above the room.
room1=room4

[room860]
descr=Required for the player to enter the forest.
room1=room860

[tunnel106]
descr=Spawns SCP-106 inside the room.
room1=tunnel

[room4tunnels]
descr=Spawns a dead body inside the room.
room1=room4tunnels

[room_gw]
descr=Required for the airlock inside the room to be functional.
room1=room2gw
room2=room3gw

[room2gw_b]
descr=Spawns a guard inside the room.
room1=room2gw_b

[room1162]
descr=Required for SCP-1162 to be functional.
room1=room1162

[room2scps2]
descr=An event where Emily Ross gets captured by SCP-106.
room1=room2scps2

[room2sl]
descr=Spawns SCP-049 inside the room.
room1=room2sl

[room2offices035]
descr=Spawns the corpse of the possessed scientist inside the room after the player has released him from the containment chamber.
room1=room2offices

[medibay]
descr=Spawns an SCP-008-1 instance inside the room.
room1=medibay

[room2shaft]
descr=Spawns a dead guard inside the room.
room1=room2shaft

[room2pit106]
descr=An event in which SCP-106 spawns at the bottom of the catwalk.
room1=room2pit

[room1archive]
descr=Required for the entrance door to be functional.
room1=room1archive

[room1lifts]
descr=Required for the elevator buttons to be clickable.
room1=room1lifts