.lib "user32.dll"

api_ActivateKeyboardLayout% (HKL%, flags%) : "ActivateKeyboardLayout"
api_AdjustWindowRect% (lpRect*, dwStyle%, bMenu%) : "AdjustWindowRect"
api_AdjustWindowRectEx% (lpRect*, dsStyle%, bMenu%, dwEsStyle%) : "AdjustWindowRectEx"
api_AnyPopup% () : "AnyPopup"
api_AppendMenu% (hMenu%, wFlags%, wIDNewItem%, lpNewItem*) : "AppendMenuA"
api_ArrangeIconicWindows% (hwnd%) : "ArrangeIconicWindows"
api_AttachThreadInput% (idAttach%, idAttachTo%, fAttach%) : "AttachThreadInput"
api_BeginDeferWindowPos% (nNumWindows%) : "BeginDeferWindowPos"
api_BeginPaint% (hwnd%, lpPaint*) : "BeginPaint"
api_BringWindowToTop% (hwnd%) : "BringWindowToTop"
api_BroadcastSystemMessage% (dw%, pdw%, un%, wParam%, lParam%) : "BroadcastSystemMessage"
api_CallMsgFilter% (lpMsg*, ncode%) : "CallMsgFilterA"
api_CallNextHookEx% (hHook%, ncode%, wParam%, lParam*) : "CallNextHookEx"
api_CallWindowProc% (lpPrevWndFunc%, hWnd%, Msg%, wParam%, lParam%) : "CallWindowProcA"
api_CascadeWindows% (hwndParent%, wHow%, lpRect*, cKids%, lpkids%) : "CascadeWindows"
api_ChangeClipboardChain% (hwnd%, hWndNext%) : "ChangeClipboardChain"
api_ChangeMenu% (hMenu%, cmd%, lpszNewItem$, cmdInsert%, flags%) : "ChangeMenuA"
api_CharLower$ (lpsz$) : "CharLowerA"
api_CharLowerBuff% (lpsz$, cchLength%) : "CharLowerBuffA"
api_CharNext$ (lpsz$) : "CharNextA"
api_CharPrev$ (lpszStart$, lpszCurrent$) : "CharPrevA"
api_CharToOem% (lpszSrc$, lpszDst$) : "CharToOemA"
api_CharToOemBuff% (lpszSrc$, lpszDst$, cchDstLength%) : "CharToOemBuffA"
api_CharUpper$ (lpsz$) : "CharUpperA"
api_CharUpperBuff% (lpsz$, cchLength%) : "CharUpperBuffA"
api_CheckDlgButton% (hDlg%, nIDButton%, wCheck%) : "CheckDLGButtonA"
api_CheckMenuItem% (hMenu%, wIDCheckItem%, wCheck%) : "CheckMenuItem"
api_CheckMenuRadioItem% (hMenu%, un1%, un2%, un3%, un4%) : "CheckMenuRadioItem"
api_CheckRadioButton% (hDlg%, nIDFirstButton%, nIDLastButton%, nIDCheckButton%) : "CheckRadioButtonA"
api_ChildWindowFromPoint% (hWnd%, xPoint%, yPoint%) : "ChildWindowFromPoint"
api_ChildWindowFromPointEx% (hWnd%, pt*, un%) : "ChildWindowFromPointEx"
api_ClientToScreen% (hwnd%, lpPoint*) : "ClientToScreen"
api_ClipCursor% (lpRect*) : "ClipCursor"
api_CloseClipboard% () : "CloseClipboard"
api_CloseDesktop% (hDesktop%) : "CloseDesktop"
api_CloseWindow% (hwnd%) : "CloseWindow"
api_CloseWindowStation% (hWinSta%) : "CloseWindowStation"
api_CopyAcceleratorTable% (hAccelSrc%, lpAccelDst*, cAccelEntries%) : "CopyAcceleratorTableA"
api_CopyCursor% (hcur%) : "CopyCursor"
api_CopyIcon% (hIcon%) : "CopyIcon"
api_CopyImage% (Handle%, un1%, n1%, n2%, un2%) : "CopyImage"
api_CopyRect% (lpDestRect*, lpSourceRect*) : "CopyRect"
api_CountClipboardFormats% () : "CountClipboardFormats"
api_CreateAcceleratorTable% (lpaccl*, cEntries%) : "CreateAcceleratorTableA"
api_CreateCaret% (hwnd%, hBitmap%, nWidth%, nHeight%) : "CreateCaret"
api_CreateCursor% (hInstance%, nXhotspot%, nYhotspot%, nWidth%, nHeight%, lpANDbitPlane*, lpXORbitPlane*) : "CreateCursor"
api_CreateDesktop% (lpszDesktop$, lpszDevice$, pDevmode*, dwFlags%, dwDesiredAccess%, lpsa*) : "CreateDesktopA"
api_CreateDialogIndirectParam% (hInstance%, lpTemplate*, hWndParent%, lpDialogFunc%, dwInitParam%) : "CreateDialogIndirectParamA"
api_CreateDialogParam% (hInstance%, lpName$, hWndParent%, lpDialogFunc%, lParamInit%) : "CreateDialogParamA"
api_CreateIcon% (hInstance%, nWidth%, nHeight%, nPlanes%, nBitsPixel%, lpANDbits%, lpXORbits%) : "CreateIcon"
api_CreateIconFromResource% (presbits%, dwResSize%, fIcon%, dwVer%) : "CreateIconFromResource"
api_CreateIconIndirect% (piconinfo*) : "CreateIconIndirect"
api_CreateMDIWindow% (lpClassName$, lpWindowName$, dwStyle%, x%, y%, nWidth%, nHeight%, hWndParent%, hInstance%, lParam%) : "CreateMDIWindowA"
api_CreateMenu% () : "CreateMenu"
api_CreatePopupMenu% () : "CreatePopupMenu"
api_CreateWindowEx% (dwExStyle%, lpClassName$, lpWindowName$, dwStyle%, x%, y%, nWidth%, nHeight%, hWndParent%, hMenu%, hInstance%, lpParam*) : "CreateWindowExA"
api_DdeAbandonTransaction% (idInst%, hConv%, idTransaction%) : "DdeAbandonTransaction"
api_DdeAccessData% (hData%, pcbDataSize%) : "DdeAccessDataA"
api_DdeAddData% (hData%, pSrc%, cb%, cbOff%) : "DdeAddDataA"
api_DdeClientTransaction% (pData%, cbData%, hConv%, hszItem%, wFmt%, wType%, dwTimeout%, pdwResult%) : "DdeClientTransaction"
api_DdeCmpStringHandles% (hsz1%, hsz2%) : "DdeCmpStringHandles"
api_DdeConnect% (idInst%, hszService%, hszTopic%, pCC*) : "DdeConnect"
api_DdeConnectList% (idInst%, hszService%, hszTopic%, hConvList%, pCC*) : "DdeConnectList"
api_DdeCreateDataHandle% (idInst%, pSrc%, cb%, cbOff%, hszItem%, wFmt%, afCmd%) : "DdeCreateDataHandle"
api_DdeCreateStringHandle% (idInst%, psz$, iCodePage%) : "DdeCreateStringHandleA"
api_DdeDisconnect% (hConv%) : "DdeDisconnect"
api_DdeDisconnectList% (hConvList%) : "DdeDisconnectList"
api_DdeEnableCallback% (idInst%, hConv%, wCmd%) : "DdeEnableCallback"
api_DdeFreeDataHandle% (hData%) : "DdeFreeDataHandle"
api_DdeFreeStringHandle% (idInst%, hsz%) : "DdeFreeStringHandle"
api_DdeGetData% (hData%, pDst%, cbMax%, cbOff%) : "DdeGetDataA"
api_DdeGetLastError% (idInst%) : "DdeGetLastError"
api_DdeImpersonateClient% (hConv%) : "DdeImpersonateClient"
api_DdeInitialize% (pidInst%, pfnCallback%, afCmd%, ulRes%) : "DdeInitializeA"
api_DdeKeepStringHandle% (idInst%, hsz%) : "DdeKeepStringHandle"
api_DdeNameService% (idInst%, hsz1%, hsz2%, afCmd%) : "DdeNameService"
api_DdePostAdvise% (idInst%, hszTopic%, hszItem%) : "DdePostAdvise"
api_DdeQueryConvInfo% (hConv%, idTransaction%, pConvInfo*) : "DdeQueryConvInfo"
api_DdeQueryNextServer% (hConvList%, hConvPrev%) : "DdeQueryNextServer"
api_DdeQueryString% (idInst%, hsz%, psz$, cchMax%, iCodePage%) : "DdeQueryStringA"
api_DdeReconnect% (hConv%) : "DdeReconnect"
api_DdeSetQualityOfService% (hWndClient%, pqosNew*, pqosPrev*) : "DdeSetQualityOfService"
api_DdeSetUserHandle% (hConv%, id%, hUser%) : "DdeSetUserHandle"
api_DdeUnaccessData% (hData%) : "DdeUnaccessDataA"
api_DdeUninitialize% (idInst%) : "DdeUninitialize"
api_DefDlgProc% (hDlg%, wMsg%, wParam%, lParam%) : "DefDlgProcA"
api_DeferWindowPos% (hWinPosInfo%, hwnd%, hWndInsertAfter%, x%, y%, cx%, cy%, wFlags%) : "DeferWindowPos"
api_DefFrameProc% (hwnd%, hWndMDIClient%, wMsg%, wParam%, lParam%) : "DefFrameProcA"
api_DefMDIChildProc% (hwnd%, wMsg%, wParam%, lParam%) : "DefMDIChildProcA"
api_DefWindowProc% (hwnd%, wMsg%, wParam%, lParam%) : "DefWindowProcA"
api_DeleteMenu% (hMenu%, nPosition%, wFlags%) : "DeleteMenu"
api_DestroyAcceleratorTable% (haccel%) : "DestroyAcceleratorTable"
api_DestroyCaret% () : "DestroyCaret"
api_DestroyCursor% (hCursor%) : "DestroyCursor"
api_DestroyIcon% (hIcon%) : "DestroyIcon"
api_DestroyMenu% (hMenu%) : "DestroyMenu"
api_DestroyWindow% (hwnd%) : "DestroyWindow"
api_DialogBoxIndirectParam% (hInstance%, hDialogTemplate*, hWndParent%, lpDialogFunc%, dwInitParam%) : "DialogBoxIndirectParamA"
api_DispatchMessage% (lpMsg*) : "DispatchMessageA"
api_DlgDirList% (hDlg%, lpPathSpec$, nIDListBox%, nIDStaticPath%, wFileType%) : "DlgDirListA"
api_DlgDirListComboBox% (hDlg%, lpPathSpec$, nIDComboBox%, nIDStaticPath%, wFileType%) : "DlgDirListComboBoxA"
api_DlgDirSelectComboBoxEx% (hWndDlg%, lpszPath$, cbPath%, idComboBox%) : "DlgDirSelectComboBoxExA"
api_DlgDirSelectEx% (hWndDlg%, lpszPath$, cbPath%, idListBox%) : "DlgDirSelectExA"
api_DragDetect% (hWnd%, pt*) : "DragDetect"
api_DragObject% (hWnd1%, hWnd2%, un%, dw%, hCursor%) : "DragObject"
api_DrawAnimatedRects% (hwnd%, idAni%, lprcFrom*, lprcTo*) : "DrawAnimatedRects"
api_DrawCaption% (hWnd%, hDC%, pcRect*, un%) : "DrawCaption"
api_DrawEdge% (hdc%, qrc*, edge%, grfFlags%) : "DrawEdge"
api_DrawFocusRect% (hdc%, lpRect*) : "DrawFocusRect"
api_DrawFrameControl% (hDC%, lpRect*, un1%, un2%) : "DrawFrameControl"
api_DrawIcon% (hdc%, x%, y%, hIcon%) : "DrawIcon"
api_DrawIconEx% (hdc%, xLeft%, yTop%, hIcon%, cxWidth%, cyWidth%, istepIfAniCur%, hbrFlickerFreeDraw%, diFlags%) : "DrawIconEx"
api_DrawMenuBar% (hwnd%) : "DrawMenuBar"
api_DrawState% (hDC%, hBrush%, lpDrawStateProc%, lParam%, wParam%, n1%, n2%, n3%, n4%, un%) : "DrawStateA"
api_DrawText% (hdc%, lpStr$, nCount%, lpRect*, wFormat%) : "DrawTextA"
api_DrawTextEx% (hDC%, lpsz$, n%, lpRect*, un%, lpDrawTextParams*) : "DrawTextExA"
api_EmptyClipboard% () : "EmptyClipboard"
api_EnableMenuItem% (hMenu%, wIDEnableItem%, wEnable%) : "EnableMenuItem"
api_EnableScrollBar% (hwnd%, wSBflags%, wArrows%) : "EnableScrollBar"
api_EnableWindow% (hwnd%, fEnable%) : "EnableWindow"
api_EndDeferWindowPos% (hWinPosInfo%) : "EndDeferWindowPos"
api_EndDialog% (hDlg%, nResult%) : "EndDialog"
api_EndPaint% (hwnd%, lpPaint*) : "EndPaint"
api_EnumChildWindows% (hWndParent%, lpEnumFunc%, lParam%) : "EnumChildWindows"
api_EnumClipboardFormats% (wFormat%) : "EnumClipboardFormats"
api_EnumDesktops% (hwinsta%, lpEnumFunc%, lParam%) : "EnumDesktopsA"
api_EnumDesktopWindows% (hDesktop%, lpfn%, lParam%) : "EnumDesktopWindows"
api_EnumProps% (hWnd%, lpEnumFunc%) : "EnumPropsA"
api_EnumPropsEx% (hWnd%, lpEnumFunc%, lParam%) : "EnumPropsExA"
api_EnumThreadWindows% (dwThreadId%, lpfn%, lParam%) : "EnumThreadWindows"
api_EnumWindowStations% (lpEnumFunc%, lParam%) : "EnumWindowStationsA"
api_EqualRect% (lpRect1*, lpRect2*) : "EqualRect"
api_ExcludeUpdateRgn% (hdc%, hwnd%) : "ExcludeUpdateRgn"
api_ExitWindows% (dwReserved%, uReturnCode%) : "ExitWindows"
api_ExitWindowsEx% (uFlags%, dwReserved%) : "ExitWindowsEx"
api_FillRect% (hdc%, lpRect*, hBrush%) : "FillRect"
api_FindWindow% (lpClassName$, lpWindowName$) : "FindWindowA"
api_FindWindowEx% (hWnd1%, hWnd2%, lpsz1$, lpsz2$) : "FindWindowExA"
api_FlashWindow% (hwnd%, bInvert%) : "FlashWindow"
api_FrameRect% (hdc%, lpRect*, hBrush%) : "FrameRect"
api_FreeDDElParam% (msg%, lParam%) : "FreeDDElParam"
api_GetActiveWindow% () : "GetActiveWindow"
api_GetAsyncKeyState% (vKey%) : "GetAsyncKeyState"
api_GetCapture% () : "GetCapture"
api_GetCaretBlinkTime% () : "GetCaretBlinkTime"
api_GetCaretPos% (lpPoint*) : "GetCaretPos"
api_GetClassInfo% (hInstance%, lpClassName$, lpWndClass*) : "GetClassInfoA"
api_GetClassLong% (hwnd%, nIndex%) : "GetClassLongA"
api_GetClassName% (hwnd%, lpClassName$, nMaxCount%) : "GetClassNameA"
api_GetClassWord% (hwnd%, nIndex%) : "GetClassWord"
api_GetClientRect% (hwnd%, lpRect*) : "GetClientRect"
api_GetClipboardData% (wFormat%) : "GetClipboardData"
api_GetClipboardFormatName% (wFormat%, lpString$, nMaxCount%) : "GetClipboardFormatNameA"
api_GetClipboardOwner% () : "GetClipboardOwner"
api_GetClipboardViewer% () : "GetClipboardViewer"
api_GetClipCursor% (lprc*) : "GetClipCursor"
api_GetCursor% () : "GetCursor"
api_GetCursorPos% (lpPoint*) : "GetCursorPos"
api_GetDC% (hwnd%) : "GetDC"
api_GetDCEx% (hwnd%, hrgnclip%, fdwOptions%) : "GetDCEx"
api_GetDesktopWindow% () : "GetDesktopWindow"
api_GetDialogBaseUnits% () : "GetDialogBaseUnits"
api_GetDlgCtrlID% (hwnd%) : "GetDlgCtrlID"
api_GetDlgItem% (hDlg%, nIDDlgItem%) : "GetDlgItem"
api_GetDlgItemInt% (hDlg%, nIDDlgItem%, lpTranslated%, bSigned%) : "GetDlgItemInt"
api_GetDlgItemText% (hDlg%, nIDDlgItem%, lpString$, nMaxCount%) : "GetDlgItemTextA"
api_GetDoubleClickTime% () : "GetDoubleClickTime"
api_GetFocus% () : "GetFocus"
api_GetForegroundWindow% () : "GetForegroundWindow"
api_GetIconInfo% (hIcon%, piconinfo*) : "GetIconInfo"
api_GetInputState% () : "GetInputState"
api_GetKBCodePage% () : "GetKBCodePage"
api_GetKeyboardLayout% (dwLayout%) : "GetKeyboardLayout"
api_GetKeyboardLayoutList% (nBuff%, lpList%) : "GetKeyboardLayoutList"
api_GetKeyboardLayoutName% (pwszKLID$) : "GetKeyboardLayoutNameA"
api_GetKeyboardState% (pbKeyState%) : "GetKeyboardState"
api_GetKeyboardType% (nTypeFlag%) : "GetKeyboardType"
api_GetKeyNameText% (lParam%, lpBuffer$, nSize%) : "GetKeyNameTextA"
api_GetKeyState% (nVirtKey%) : "GetKeyState"
api_GetLastActivePopup% (hwndOwnder%) : "GetLastActivePopup"
api_GetMenu% (hwnd%) : "GetMenu"
api_GetMenuCheckMarkDimensions% () : "GetMenuCheckMarkDimensions"
api_GetMenuContextHelpId% (hMenu%) : "GetMenuContextHelpId"
api_GetMenuDefaultItem% (hMenu%, fByPos%, gmdiFlags%) : "GetMenuDefaultItem"
api_GetMenuItemCount% (hMenu%) : "GetMenuItemCount"
api_GetMenuItemID% (hMenu%, nPos%) : "GetMenuItemID"
api_GetMenuItemInfo% (hMenu%, un%, b%, lpMenuItemInfo*) : "GetMenuItemInfoA"
api_GetMenuItemRect% (hWnd%, hMenu%, uItem%, lprcItem*) : "GetMenuItemRect"
api_GetMenuState% (hMenu%, wID%, wFlags%) : "GetMenuState"
api_GetMenuString% (hMenu%, wIDItem%, lpString$, nMaxCount%, wFlag%) : "GetMenuStringA"
api_GetMessage% (lpMsg*, hwnd%, wMsgFilterMin%, wMsgFilterMax%) : "GetMessageA"
api_GetMessageExtraInfo% () : "GetMessageExtraInfo"
api_GetMessagePos% () : "GetMessagePos"
api_GetMessageTime% () : "GetMessageTime"
api_GetNextDlgGroupItem% (hDlg%, hCtl%, bPrevious%) : "GetNextDlgGroupItem"
api_GetNextDlgTabItem% (hDlg%, hCtl%, bPrevious%) : "GetNextDlgTabItem"
api_GetNextWindow% (hwnd%, wFlag%) : "GetWindow"
api_GetOpenClipboardWindow% () : "GetOpenClipboardWindow"
api_GetParent% (hwnd%) : "GetParent"
api_GetPriorityClipboardFormat% (lpPriorityList%, nCount%) : "GetPriorityClipboardFormat"
api_GetProcessWindowStation% () : "GetProcessWindowStation"
api_GetProp% (hwnd%, lpString$) : "GetPropA"
api_GetQueueStatus% (fuFlags%) : "GetQueueStatus"
api_GetScrollInfo% (hWnd%, n%, lpScrollInfo*) : "GetScrollInfo"
api_GetScrollPos% (hwnd%, nBar%) : "GetScrollPos"
api_GetScrollRange% (hwnd%, nBar%, lpMinPos%, lpMaxPos%) : "GetScrollRange"
api_GetSubMenu% (hMenu%, nPos%) : "GetSubMenu"
api_GetSysColor% (nIndex%) : "GetSysColor"
api_GetSysColorBrush% (nIndex%) : "GetSysColorBrush"
api_GetSystemMenu% (hwnd%, bRevert%) : "GetSystemMenu"
api_GetSystemMetrics% (nIndex%) : "GetSystemMetrics"
api_GetTabbedTextExtent% (hdc%, lpString$, nCount%, nTabPositions%, lpnTabStopPositions%) : "GetTabbedTextExtentA"
api_GetThreadDesktop% (dwThread%) : "GetThreadDesktop"
api_GetTopWindow% (hwnd%) : "GetTopWindow"
api_GetUpdateRect% (hwnd%, lpRect*, bErase%) : "GetUpdateRect"
api_GetUpdateRgn% (hwnd%, hRgn%, fErase%) : "GetUpdateRgn"
api_GetUserObjectInformation% (hObj%, nIndex%, pvInfo*, nLength%, lpnLengthNeeded%) : "GetUserObjectInformationA"
api_GetUserObjectSecurity% (hObj%, pSIRequested%, pSd*, nLength%, lpnLengthNeeded%) : "GetUserObjectSecurity"
api_GetWindow% (hwnd%, wCmd%) : "GetWindow"
api_GetWindowContextHelpId% (hWnd%) : "GetWindowContextHelpId"
api_GetWindowDC% (hwnd%) : "GetWindowDC"
api_GetWindowLong% (hwnd%, nIndex%) : "GetWindowLongA"
api_GetWindowPlacement% (hwnd%, lpwndpl*) : "GetWindowPlacement"
api_GetWindowRect% (hwnd%, lpRect*) : "GetWindowRect"
api_GetWindowRgn% (hWnd%, hRgn%) : "GetWindowRgn"
api_GetWindowText% (hwnd%, lpString$, cch%) : "GetWindowTextA"
api_GetWindowTextLength% (hwnd%) : "GetWindowTextLengthA"
api_GetWindowThreadProcessId% (hwnd%, lpdwProcessId%) : "GetWindowThreadProcessId"
api_GetWindowWord% (hwnd%, nIndex%) : "GetWindowWord"
api_GrayString% (hDC%, hBrush%, lpOutputFunc%, lpData%, nCount%, X%, Y%, nWidth%, nHeight%) : "GrayStringA"
api_HideCaret% (hwnd%) : "HideCaret"
api_HiliteMenuItem% (hwnd%, hMenu%, wIDHiliteItem%, wHilite%) : "HiliteMenuItem"
api_ImpersonateDdeClientWindow% (hWndClient%, hWndServer%) : "ImpersonateDdeClientWindow"
api_InflateRect% (lpRect*, x%, y%) : "InflateRect"
api_InSendMessage% () : "InSendMessage"
api_InsertMenu% (hMenu%, nPosition%, wFlags%, wIDNewItem%, lpNewItem*) : "InsertMenuA"
api_InsertMenuItem% (hMenu%, un%, bool%, lpcMenuItemInfo*) : "InsertMenuItemA"
api_IntersectRect% (lpDestRect*, lpSrc1Rect*, lpSrc2Rect*) : "IntersectRect"
api_InvalidateRect% (hwnd%, lpRect*, bErase%) : "InvalidateRect"
api_InvalidateRgn% (hwnd%, hRgn%, bErase%) : "InvalidateRgn"
api_InvertRect% (hdc%, lpRect*) : "InvertRect"
api_IsCharAlpha% (cChar%) : "IsCharAlphaA"
api_IsCharAlphaNumeric% (cChar%) : "IsCharAlphaNumericA"
api_IsCharLower% (cChar%) : "IsCharLowerA"
api_IsCharUpper% (cChar%) : "IsCharUpperA"
api_IsChild% (hWndParent%, hwnd%) : "IsChild"
api_IsClipboardFormatAvailable% (wFormat%) : "IsClipboardFormatAvailable"
api_IsDialogMessage% (hDlg%, lpMsg*) : "IsDialogMessageA"
api_IsDlgButtonChecked% (hDlg%, nIDButton%) : "IsDlgButtonChecked"
api_IsIconic% (hwnd%) : "IsIconic"
api_IsMenu% (hMenu%) : "IsMenu"
api_IsRectEmpty% (lpRect*) : "IsRectEmpty"
api_IsWindow% (hwnd%) : "IsWindow"
api_IsWindowEnabled% (hwnd%) : "IsWindowEnabled"
api_IsWindowUnicode% (hwnd%) : "IsWindowUnicode"
api_IsWindowVisible% (hwnd%) : "IsWindowVisible"
api_IsZoomed% (hwnd%) : "IsZoomed"
api_keybd_event (bVk%, bScan%, dwFlags%, dwExtraInfo%) : "keybd_event"
api_KillTimer% (hwnd%, nIDEvent%) : "KillTimer"
api_LoadAccelerators% (hInstance%, lpTableName$) : "LoadAcceleratorsA"
api_LoadBitmap% (hInstance%, lpBitmapName$) : "LoadBitmapA"
api_LoadCursor% (hInstance%, lpCursorName$) : "LoadCursorA"
api_LoadCursorFromFile% (lpFileName$) : "LoadCursorFromFileA"
api_LoadIcon% (hInstance%, lpIconName$) : "LoadIconA"
api_LoadImage% (hInst%, lpsz$, un1%, n1%, n2%, un2%) : "LoadImageA"
api_LoadKeyboardLayout% (pwszKLID$, flags%) : "LoadKeyboardLayoutA"
api_LoadMenu% (hInstance%, lpString$) : "LoadMenuA"
api_LoadMenuIndirect% (lpMenuTemplate%) : "LoadMenuIndirectA"
api_LoadString% (hInstance%, wID%, lpBuffer$, nBufferMax%) : "LoadStringA"
api_LockWindowUpdate% (hwndLock%) : "LockWindowUpdate"
api_LookupIconIdFromDirectory% (presbits%, fIcon%) : "LookupIconIdFromDirectory"
api_LookupIconIdFromDirectoryEx% (presbits%, fIcon%, cxDesired%, cyDesired%, Flags%) : "LookupIconIdFromDirectoryEx"
api_MapDialogRect% (hDlg%, lpRect*) : "MapDialogRect"
api_MapVirtualKey% (wCode%, wMapType%) : "MapVirtualKeyA"
api_MapVirtualKeyEx% (uCode%, uMapType%, dwhkl%) : "MapVirtualKeyExA"
api_MapWindowPoints% (hwndFrom%, hwndTo%, lppt*, cPoints%) : "MapWindowPoints"
api_MenuItemFromPoint% (hWnd%, hMenu%, ptScreen*) : "MenuItemFromPoint"
api_MessageBeep% (wType%) : "MessageBeep"
api_MessageBox% (hwnd%, lpText$, lpCaption$, wType%) : "MessageBoxA"
api_MessageBoxEx% (hwnd%, lpText$, lpCaption$, uType%, wLanguageId%) : "MessageBoxExA"
api_MessageBoxIndirect% (lpMsgBoxParams*) : "MessageBoxIndirectA"
api_ModifyMenu% (hMenu%, nPosition%, wFlags%, wIDNewItem%, lpString*) : "ModifyMenuA"
api_mouse_event (dwFlags%, dx%, dy%, cButtons%, dwExtraInfo%) : "mouse_event"
api_MoveWindow% (hwnd%, x%, y%, nWidth%, nHeight%, bRepaint%) : "MoveWindow"
api_MsgWaitForMultipleObjects% (nCount%, pHandles%, fWaitAll%, dwMilliseconds%, dwWakeMask%) : "MsgWaitForMultipleObjects"
api_OemKeyScan% (wOemChar%) : "OemKeyScan"
api_OemToChar% (lpszSrc$, lpszDst$) : "OemToCharA"
api_OemToCharBuff% (lpszSrc$, lpszDst$, cchDstLength%) : "OemToCharBuffA"
api_OffsetRect% (lpRect*, x%, y%) : "OffsetRect"
api_OpenClipboard% (hwnd%) : "OpenClipboard"
api_OpenDesktop% (lpszDesktop$, dwFlags%, fInherit%, dwDesiredAccess%) : "OpenDesktopA"
api_OpenIcon% (hwnd%) : "OpenIcon"
api_OpenInputDesktop% (dwFlags%, fInherit%, dwDesiredAccess%) : "OpenInputDesktop"
api_OpenWindowStation% (lpszWinSta$, fInherit%, dwDesiredAccess%) : "OpenWindowStationA"
api_PackDDElParam% (msg%, uiLo%, uiHi%) : "PackDDElParam"
api_PaintDesktop% (hdc%) : "PaintDesktop"
api_PeekMessage% (lpMsg*, hwnd%, wMsgFilterMin%, wMsgFilterMax%, wRemoveMsg%) : "PeekMessageA"
api_PostMessage% (hwnd%, wMsg%, wParam%, lParam%) : "PostMessageA"
api_PostQuitMessage (nExitCode%) : "PostQuitMessage"
api_PostThreadMessage% (idThread%, msg%, wParam%, lParam%) : "PostThreadMessageA"
api_PtInRect% (lpRect*, pt*) : "PtInRect"
api_RedrawWindow% (hwnd%, lprcUpdate*, hrgnUpdate%, fuRedraw%) : "RedrawWindow"
api_RegisterClass% (Class*) : "RegisterClass"
api_RegisterClassEx% (pcWndClassEx*) : "RegisterClassExA"
api_RegisterClipboardFormat% (lpString$) : "RegisterClipboardFormatA"
api_RegisterHotKey% (hwnd%, id%, fsModifiers%, vk%) : "RegisterHotKey"
api_RegisterWindowMessage% (lpString$) : "RegisterWindowMessageA"
api_ReleaseCapture% () : "ReleaseCapture"
api_ReleaseDC% (hwnd%, hdc%) : "ReleaseDC"
api_RemoveMenu% (hMenu%, nPosition%, wFlags%) : "RemoveMenu"
api_RemoveProp% (hwnd%, lpString$) : "RemovePropA"
api_ReplyMessage% (lReply%) : "ReplyMessage"
api_ReuseDDElParam% (lParam%, msgIn%, msgOut%, uiLo%, uiHi%) : "ReuseDDElParam"
api_ScreenToClient% (hwnd%, lpPoint*) : "ScreenToClient"
api_ScrollDC% (hdc%, dx%, dy%, lprcScroll*, lprcClip*, hrgnUpdate%, lprcUpdate*) : "ScrollDC"
api_ScrollWindow% (hWnd%, XAmount%, YAmount%, lpRect*, lpClipRect*) : "ScrollWindow"
api_ScrollWindowEx% (hwnd%, dx%, dy%, lprcScroll*, lprcClip*, hrgnUpdate%, lprcUpdate*, fuScroll%) : "ScrollWindowEx"
api_SendDlgItemMessage% (hDlg%, nIDDlgItem%, wMsg%, wParam%, lParam%) : "SendDlgItemMessageA"
api_SendMessage% (hwnd%, wMsg%, wParam%, lParam*) : "SendMessageA"
api_SendMessageCallback% (hwnd%, msg%, wParam%, lParam%, lpResultCallBack%, dwData%) : "SendMessageCallbackA"
api_SendMessageTimeout% (hwnd%, msg%, wParam%, lParam%, fuFlags%, uTimeout%, lpdwResult%) : "SendMessageTimeoutA"
api_SendNotifyMessage% (hwnd%, msg%, wParam%, lParam%) : "SendNotifyMessageA"
api_SetActiveWindow% (hwnd%) : "SetActiveWindow"
api_SetCapture% (hwnd%) : "SetCapture"
api_SetCaretBlinkTime% (wMSeconds%) : "SetCaretBlinkTime"
api_SetCaretPos% (x%, y%) : "SetCaretPos"
api_SetClassLong% (hwnd%, nIndex%, dwNewLong%) : "SetClassLongA"
api_SetClassWord% (hwnd%, nIndex%, wNewWord%) : "SetClassWord"
api_SetClipboardData% (wFormat%, hMem%) : "SetClipboardDataA"
api_SetClipboardViewer% (hwnd%) : "SetClipboardViewer"
api_SetCursor% (hCursor%) : "SetCursor"
api_SetCursorPos% (x%, y%) : "SetCursorPos"
api_SetDebugErrorLevel (dwLevel%) : "SetDebugErrorLevel"
api_SetDlgItemInt% (hDlg%, nIDDlgItem%, wValue%, bSigned%) : "SetDlgItemInt"
api_SetDlgItemText% (hDlg%, nIDDlgItem%, lpString$) : "SetDlgItemTextA"
api_SetDoubleClickTime% (wCount%) : "SetDoubleClickTime"
api_SetFocus% (hwnd%) : "SetFocus"
api_SetForegroundWindow% (hwnd%) : "SetForegroundWindow"
api_SetKeyboardState% (lppbKeyState%) : "SetKeyboardState"
api_SetLastErrorEx (dwErrCode%, dwType%) : "SetLastErrorEx"
api_SetMenu% (hwnd%, hMenu%) : "SetMenu"
api_SetMenuContextHelpId% (hMenu%, dw%) : "SetMenuContextHelpId"
api_SetMenuDefaultItem% (hMenu%, uItem%, fByPos%) : "SetMenuDefaultItem"
api_SetMenuItemBitmaps% (hMenu%, nPosition%, wFlags%, hBitmapUnchecked%, hBitmapChecked%) : "SetMenuItemBitmaps"
api_SetMenuItemInfo% (hMenu%, un%, bool%, lpcMenuItemInfo*) : "SetMenuItemInfoA"
api_SetMessageExtraInfo% (lParam%) : "SetMessageExtraInfo"
api_SetMessageQueue% (cMessagesMax%) : "SetMessageQueue"
api_SetParent% (hWndChild%, hWndNewParent%) : "SetParent"
api_SetProcessWindowStation% (hWinSta%) : "SetProcessWindowStation"
api_SetProp% (hwnd%, lpString$, hData%) : "SetPropA"
api_SetRect% (lpRect*, X1%, Y1%, X2%, Y2%) : "SetRect"
api_SetRectEmpty% (lpRect*) : "SetRectEmpty"
api_SetScrollInfo% (hWnd%, n%, lpcScrollInfo*, bool%) : "SetScrollInfo"
api_SetScrollPos% (hwnd%, nBar%, nPos%, bRedraw%) : "SetScrollPos"
api_SetScrollRange% (hwnd%, nBar%, nMinPos%, nMaxPos%, bRedraw%) : "SetScrollRange"
api_SetSysColors% (nChanges%, lpSysColor%, lpColorValues%) : "SetSysColors"
api_SetSystemCursor% (hcur%, id%) : "SetSystemCursor"
api_SetThreadDesktop% (hDesktop%) : "SetThreadDesktop"
api_SetTimer% (hWnd%, nIDEvent%, uElapse%, lpTimerFunc%) : "SetTimer"
api_SetUserObjectInformation% (hObj%, nIndex%, pvInfo*, nLength%) : "SetUserObjectInformationA"
api_SetUserObjectSecurity% (hObj%, pSIRequested%, pSd*) : "SetUserObjectSecurity"
api_SetWindowContextHelpId% (hWnd%, dw%) : "SetWindowContextHelpId"
api_SetWindowLong% (hwnd%, nIndex%, dwNewLong%) : "SetWindowLongA"
api_SetWindowPlacement% (hwnd%, lpwndpl*) : "SetWindowPlacement"
api_SetWindowPos% (hwnd%, hWndInsertAfter%, x%, y%, cx%, cy%, wFlags%) : "SetWindowPos"
api_SetWindowRgn% (hWnd%, hRgn%, bRedraw%) : "SetWindowRgn"
api_SetWindowsHook% (nFilterType%, pfnFilterProc%) : "SetWindowsHookA"
api_SetWindowsHookEx% (idHook%, lpfn%, hmod%, dwThreadId%) : "SetWindowsHookExA"
api_SetWindowText% (hwnd%, lpString$) : "SetWindowTextA"
api_SetWindowWord% (hwnd%, nIndex%, wNewWord%) : "SetWindowWord"
api_ShowCaret% (hwnd%) : "ShowCaret"
api_ShowCursor% (bShow%) : "ShowCursor"
api_ShowOwnedPopups% (hwnd%, fShow%) : "ShowOwnedPopups"
api_ShowScrollBar% (hwnd%, wBar%, bShow%) : "ShowScrollBar"
api_ShowWindow% (hwnd%, nCmdShow%) : "ShowWindow"
api_ShowWindowAsync% (hWnd%, nCmdShow%) : "ShowWindowAsync"
api_SubtractRect% (lprcDst*, lprcSrc1*, lprcSrc2*) : "SubtractRect"
api_SwapMouseButton% (bSwap%) : "SwapMouseButton"
api_SwitchDesktop% (hDesktop%) : "SwitchDesktop"
api_SystemParametersInfo% (uAction%, uParam%, lpvParam$, fuWinIni%) : "SystemParametersInfoA"
api_TabbedTextOut% (hdc%, x%, y%, lpString$, nCount%, nTabPositions%, lpnTabStopPositions%, nTabOrigin%) : "TabbedTextOutA"
api_TileWindows% (hwndParent%, wHow%, lpRect*, cKids%, lpKids%) : "TileWindows"
api_ToAscii% (uVirtKey%, uScanCode%, lpbKeyState%, lpwTransKey%, fuState%) : "ToAscii"
api_ToAsciiEx% (uVirtKey%, uScanCode%, lpKeyState%, lpChar%, uFlags%, dwhkl%) : "ToAsciiEx"
api_ToUnicode% (wVirtKey%, wScanCode%, lpKeyState%, pwszBuff$, cchBuff%, wFlags%) : "ToUnicode"
api_TrackPopupMenu% (hMenu%, wFlags%, x%, y%, nReserved%, hwnd%, lprc*) : "TrackPopupMenu"
api_TrackPopupMenuEx% (hMenu%, un%, n1%, n2%, hWnd%, lpTPMParams*) : "TrackPopupMenuEx"
api_TranslateAccelerator% (hwnd%, hAccTable%, lpMsg*) : "TranslateAcceleratorA"
api_TranslateMDISysAccel% (hWndClient%, lpMsg*) : "TranslateMDISysAccel"
api_TranslateMessage% (lpMsg*) : "TranslateMessage"
api_UnhookWindowsHook% (nCode%, pfnFilterProc%) : "UnhookWindowsHook"
api_UnhookWindowsHookEx% (hHook%) : "UnhookWindowsHookEx"
api_UnionRect% (lpDestRect*, lpSrc1Rect*, lpSrc2Rect*) : "UnionRect"
api_UnloadKeyboardLayout% (HKL%) : "UnloadKeyboardLayout"
api_UnpackDDElParam% (msg%, lParam%, puiLo%, puiHi%) : "UnpackDDElParam"
api_UnregisterClass% (lpClassName$, hInstance%) : "UnregisterClassA"
api_UnregisterHotKey% (hwnd%, id%) : "UnregisterHotKey"
api_UpdateWindow% (hwnd%) : "UpdateWindow"
api_ValidateRect% (hwnd%, lpRect*) : "ValidateRect"
api_ValidateRgn% (hwnd%, hRgn%) : "ValidateRgn"
api_VkKeyScan% (cChar%) : "VkKeyScanA"
api_VkKeyScanEx% (ch%, dwhkl%) : "VkKeyScanExA"
api_WaitForInputIdle% (hProcess%, dwMilliseconds%) : "WaitForInputIdle"
api_WaitMessage% () : "WaitMessage"
api_WindowFromDC% (hdc%) : "WindowFromDC"
api_WindowFromPoint% (xPoint%, yPoint%) : "WindowFromPoint"
api_WinHelp% (hwnd%, lpHelpFile$, wCommand%, dwData%) : "WinHelpA"