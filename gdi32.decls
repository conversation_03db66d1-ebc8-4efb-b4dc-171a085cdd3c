.lib "gdi32.dll"

api_AbortDoc% (hdc%) : "AbortDoc"
api_AbortPath% (hdc%) : "AbortPath"
api_AddFontResource% (lpFileName$) : "AddFontResourceA"
api_AngleArc% (hdc%, x%, y%, dwRadius%, eStartAngle*, eSweepAngle*) : "AngleArc"
api_AnimatePalette% (hPalette%, wStartIndex%, wNumEntries%, lpPaletteColors*) : "AnimatePaletteA"
api_Arc% (hdc%, X1%, Y1%, X2%, Y2%, X3%, Y3%, X4%, Y4%) : "Arc"
api_ArcTo% (hdc%, X1%, Y1%, X2%, Y2%, X3%, Y3%, X4%, Y4%) : "ArcTo"
api_BeginPath% (hdc%) : "BeginPath"
api_BitBlt% (hDestDC%, x%, y%, nWidth%, nHeight%, hSrcDC%, xSrc%, ySrc%, dwRop%) : "BitBlt"
api_CancelDC% (hdc%) : "CancelDC"
api_CheckColorsInGamut% (hdc%, lpv*, lpv2*, dw%) : "CheckColorsInGamut"
api_ChoosePixelFormat% (hDC%, pPixelFormatDescriptor*) : "ChoosePixelFormat"
api_Chord% (hdc%, X1%, Y1%, X2%, Y2%, X3%, Y3%, X4%, Y4%) : "Chord"
api_CloseEnhMetaFile% (hdc%) : "CloseEnhMetaFile"
api_CloseFigure% (hdc%) : "CloseFigure"
api_CloseMetaFile% (hMF%) : "CloseMetaFile"
api_ColorMatchToTarget% (hdc%, hdc2%, dw%) : "ColorMatchToTarget"
api_CombineRgn% (hDestRgn%, hSrcRgn1%, hSrcRgn2%, nCombineMode%) : "CombineRgn"
api_CombineTransform% (lpxformResult*, lpxform1*, lpxform2*) : "CombineTransform"
api_CopyEnhMetaFile% (hemfSrc%, lpszFile$) : "CopyEnhMetaFileA"
api_CopyMetaFile% (hMF%, lpFileName$) : "CopyMetaFileA"
api_CreateBitmap% (nWidth%, nHeight%, nPlanes%, nBitCount%, lpBits*) : "CreateBitmap"
api_CreateBitmapIndirect% (lpBitmap*) : "CreateBitmapIndirect"
api_CreateBrushIndirect% (lpLogBrush*) : "CreateBrushIndirect"
api_CreateColorSpace% (lplogcolorspace*) : "CreateColorSpaceA"
api_CreateCompatibleBitmap% (hdc%, nWidth%, nHeight%) : "CreateCompatibleBitmap"
api_CreateCompatibleDC% (hdc%) : "CreateCompatibleDC"
api_CreateDC% (lpDriverName$, lpDeviceName$, lpOutput$, lpInitData*) : "CreateDCA"
api_CreateDIBitmap% (hdc%, lpInfoHeader*, dwUsage%, lpInitBits*, lpInitInfo*, wUsage%) : "CreateDIBitmap"
api_CreateDIBPatternBrush% (hPackedDIB%, wUsage%) : "CreateDIBPatternBrush"
api_CreateDIBPatternBrushPt% (lpPackedDIB*, iUsage%) : "CreateDIBPatternBrushPt"
api_CreateDIBSection% (hDC%, pBitmapInfo*, un%, lplpVoid%, handle%, dw%) : "CreateDIBSection"
api_CreateDiscardableBitmap% (hdc%, nWidth%, nHeight%) : "CreateDiscardableBitmap"
api_CreateEllipticRgn% (X1%, Y1%, X2%, Y2%) : "CreateEllipticRgn"
api_CreateEllipticRgnIndirect% (lpRect*) : "CreateEllipticRgnIndirect"
api_CreateEnhMetaFile% (hdcRef%, lpFileName$, lpRect*, lpDescription$) : "CreateEnhMetaFileA"
api_CreateFont% (H%, W%, E%, O%, W%, I%, u%, S%, C%, OP%, CP%, Q%, PAF%, F$) : "CreateFontA"
api_CreateFontIndirect% (lpLogFont*) : "CreateFontIndirectA"
api_CreateHalftonePalette% (hdc%) : "CreateHalftonePalette"
api_CreateHatchBrush% (nIndex%, crColor%) : "CreateHatchBrush"
api_CreateIC% (lpDriverName$, lpDeviceName$, lpOutput$, lpInitData*) : "CreateICA"
api_CreateMetaFile% (lpString$) : "CreateMetaFileA"
api_CreatePalette% (lpLogPalette*) : "CreatePalette"
api_CreatePatternBrush% (hBitmap%) : "CreatePatternBrush"
api_CreatePen% (nPenStyle%, nWidth%, crColor%) : "CreatePen"
api_CreatePenIndirect% (lpLogPen*) : "CreatePenIndirect"
api_CreatePolygonRgn% (lpPoint*, nCount%, nPolyFillMode%) : "CreatePolygonRgn"
api_CreatePolyPolygonRgn% (lpPoint*, lpPolyCounts%, nCount%, nPolyFillMode%) : "CreatePolyPolygonRgn"
api_CreateRectRgn% (X1%, Y1%, X2%, Y2%) : "CreateRectRgn"
api_CreateRectRgnIndirect% (lpRect*) : "CreateRectRgnIndirect"
api_CreateRoundRectRgn% (X1%, Y1%, X2%, Y2%, X3%, Y3%) : "CreateRoundRectRgn"
api_CreateScalableFontResource% (fHidden%, lpszResourceFile$, lpszFontFile$, lpszCurrentPath$) : "CreateScalableFontResourceA"
api_CreateSolidBrush% (crColor%) : "CreateSolidBrush"
api_DeleteColorSpace% (hcolorspace%) : "DeleteColorSpace"
api_DeleteDC% (hdc%) : "DeleteDC"
api_DeleteEnhMetaFile% (hemf%) : "DeleteEnhMetaFile"
api_DeleteMetaFile% (hMF%) : "DeleteMetaFile"
api_DeleteObject% (hObject%) : "DeleteObject"
api_DescribePixelFormat% (hDC%, n%, un%, lpPixelFormatDescriptor*) : "DescribePixelFormat"
api_DPtoLP% (hdc%, lpPoint*, nCount%) : "DPtoLP"
api_DrawEscape% (hdc%, nEscape%, cbInput%, lpszInData$) : "DrawEscape"
api_Ellipse% (hdc%, X1%, Y1%, X2%, Y2%) : "Ellipse"
api_EndDoc% (hdc%) : "EndDoc"
api_EndPage% (hdc%) : "EndPage"
api_EndPath% (hdc%) : "EndPath"
api_EnumEnhMetaFile% (hdc%, hemf%, lpEnhMetaFunc%, lpData*, lpRect*) : "EnumEnhMetaFile"
api_EnumFontFamilies% (hdc%, lpszFamily$, lpEnumFontFamProc%, lParam%) : "EnumFontFamiliesA"
api_EnumFontFamiliesEx% (hdc%, lpLogFont*, lpEnumFontProc%, lParam%, dw%) : "EnumFontFamiliesExA"
api_EnumFonts% (hDC%, lpsz$, lpFontEnumProc%, lParam%) : "EnumFontsA"
api_EnumICMProfiles% (hdc%, icmEnumProc%, lParam%) : "EnumICMProfilesA"
api_EnumMetaFile% (hDC%, hMetafile%, lpMFEnumProc%, lParam%) : "EnumMetaFile"
api_EnumObjects% (hDC%, n%, lpGOBJEnumProc%, lpVoid*) : "EnumObjects"
api_EqualRgn% (hSrcRgn1%, hSrcRgn2%) : "EqualRgn"
api_Escape% (hdc%, nEscape%, nCount%, lpInData$, lpOutData*) : "Escape"
api_ExcludeClipRect% (hdc%, X1%, Y1%, X2%, Y2%) : "ExcludeClipRect"
api_ExtCreatePen% (dwPenStyle%, dwWidth%, lplb*, dwStyleCount%, lpStyle%) : "ExtCreatePen"
api_ExtCreateRegion% (lpXform*, nCount%, lpRgnData*) : "ExtCreateRegion"
api_ExtEscape% (hdc%, nEscape%, cbInput%, lpszInData$, cbOutput%, lpszOutData$) : "ExtEscape"
api_ExtFloodFill% (hdc%, x%, y%, crColor%, wFillType%) : "ExtFloodFill"
api_ExtSelectClipRgn% (hdc%, hRgn%, fnMode%) : "ExtSelectClipRgn"
api_ExtTextOut% (hdc%, x%, y%, wOptions%, lpRect*, lpString$, nCount%, lpDx%) : "ExtTextOutA"
api_FillPath% (hdc%) : "FillPath"
api_FillRgn% (hdc%, hRgn%, hBrush%) : "FillRgn"
api_FixBrushOrgEx% (hDC%, n1%, n2%, lpPoint*) : "FixBrushOrgEx"
api_FlattenPath% (hdc%) : "FlattenPath"
api_FloodFill% (hdc%, x%, y%, crColor%) : "FloodFill"
api_FrameRgn% (hdc%, hRgn%, hBrush%, nWidth%, nHeight%) : "FrameRgn"
api_GdiComment% (hdc%, cbSize%, lpData%) : "GdiComment"
api_GdiFlush% () : "GdiFlush"
api_GdiGetBatchLimit% () : "GdiGetBatchLimit"
api_GdiSetBatchLimit% (dwLimit%) : "GdiSetBatchLimit"
api_GetArcDirection% (hdc%) : "GetArcDirection"
api_GetAspectRatioFilterEx% (hdc%, lpAspectRatio*) : "GetAspectRatioFilterEx"
api_GetBitmapBits% (hBitmap%, dwCount%, lpBits*) : "GetBitmapBits"
api_GetBitmapDimensionEx% (hBitmap%, lpDimension*) : "GetBitmapDimensionEx"
api_GetBkColor% (hdc%) : "GetBkColor"
api_GetBkMode% (hdc%) : "GetBkMode"
api_GetBoundsRect% (hdc%, lprcBounds*, flags%) : "GetBoundsRect"
api_GetBrushOrgEx% (hDC%, lpPoint*) : "GetBrushOrgEx"
api_GetCharABCWidths% (hdc%, uFirstChar%, uLastChar%, lpabc*) : "GetCharABCWidthsA"
api_GetCharABCWidthsFloat% (hdc%, iFirstChar%, iLastChar%, lpABCF*) : "GetCharABCWidthsFloatA"
api_GetCharacterPlacement% (hdc%, lpsz$, n1%, n2%, lpGcpResults*, dw%) : " GetCharacterPlacementA"
api_GetCharWidth% (hdc%, wFirstChar%, wLastChar%, lpBuffer%) : "GetCharWidthA"
api_GetCharWidth32% (hdc%, iFirstChar%, iLastChar%, lpBuffer%) : "GetCharWidth32A"
api_GetCharWidthFloat% (hdc%, iFirstChar%, iLastChar%, pxBuffer*) : "GetCharWidthFloatA"
api_GetClipBox% (hdc%, lpRect*) : "GetClipBox"
api_GetClipRgn% (hdc%, hRgn%) : "GetClipRgn"
api_GetColorAdjustment% (hdc%, lpca*) : "GetColorAdjustment"
api_GetColorSpace% (hdc%) : "GetColorSpace"
api_GetCurrentObject% (hdc%, uObjectType%) : "GetCurrentObject"
api_GetCurrentPositionEx% (hdc%, lpPoint*) : "GetCurrentPositionEx"
api_GetDCOrgEx% (hdc%, lpPoint*) : "GetDCOrgEx"
api_GetDeviceCaps% (hdc%, nIndex%) : "GetDeviceCaps"
api_GetDeviceGammaRamp% (hdc%, lpv*) : "GetDeviceGammaRamp"
api_GetDIBColorTable% (hDC%, un1%, un2%, pRGBQuad*) : "GetDIBColorTable"
api_GetDIBits% (aHDC%, hBitmap%, nStartScan%, nNumScans%, lpBits*, lpBI*, wUsage%) : "GetDIBits"
api_GetEnhMetaFile% (lpszMetaFile$) : "GetEnhMetaFileA"
api_GetEnhMetaFileBits% (hemf%, cbBuffer%, lpbBuffer%) : "GetEnhMetaFileBits"
api_GetEnhMetaFileDescription% (hemf%, cchBuffer%, lpszDescription$) : "GetEnhMetaFileDescriptionA"
api_GetEnhMetaFileHeader% (hemf%, cbBuffer%, lpemh*) : "GetEnhMetaFileHeader"
api_GetEnhMetaFilePaletteEntries% (hemf%, cEntries%, lppe*) : "GetEnhMetaFilePaletteEntries"
api_GetFontData% (hdc%, dwTable%, dwOffset%, lpvBuffer*, cbData%) : "GetFontDataA"
api_GetFontLanguageInfo% (hdc%) : "GetFontLanguageInfo"
api_GetGlyphOutline% (hdc%, uChar%, fuFormat%, lpgm*, cbBuffer%, lpBuffer*, lpmat2*) : "GetGlyphOutlineA"
api_GetGraphicsMode% (hdc%) : "GetGraphicsMode"
api_GetICMProfile% (hdc%, dw%, lpStr$) : "GetICMProfileA"
api_GetKerningPairs% (hdc%, cPairs%, lpkrnpair*) : "GetKerningPairsA"
api_GetLogColorSpace% (hcolorspace%, lplogcolorspace*, dw%) : "GetLogColorSpaceA"
api_GetMapMode% (hdc%) : "GetMapMode"
api_GetMetaFile% (lpFileName$) : "GetMetaFileA"
api_GetMetaFileBitsEx% (hMF%, nSize%, lpvData*) : "GetMetaFileBitsEx"
api_GetMetaRgn% (hdc%, hRgn%) : "GetMetaRgn"
api_GetMiterLimit% (hdc%, peLimit*) : "GetMiterLimit"
api_GetNearestColor% (hdc%, crColor%) : "GetNearestColor"
api_GetNearestPaletteIndex% (hPalette%, crColor%) : "GetNearestPaletteIndex"
api_GetObject% (hObject%, nCount%, lpObject*) : "GetObjectA"
api_GetObjectType% (hgdiobj%) : "GetObjectType"
api_GetOutlineTextMetrics% (hdc%, cbData%, lpotm*) : "GetOutlineTextMetricsA"
api_GetPaletteEntries% (hPalette%, wStartIndex%, wNumEntries%, lpPaletteEntries*) : "GetPaletteEntries"
api_GetPath% (hdc%, lpPoint*, lpTypes%, nSize%) : "GetPath"
api_GetPixel% (hdc%, x%, y%) : "GetPixel"
api_GetPixelFormat% (hDC%) : "GetPixelFormat"
api_GetPolyFillMode% (hdc%) : "GetPolyFillMode"
api_GetRasterizerCaps% (lpraststat*, cb%) : "GetRasterizerCaps"
api_GetRegionData% (hRgn%, dwCount%, lpRgnData*) : "GetRegionDataA"
api_GetRgnBox% (hRgn%, lpRect*) : "GetRgnBox"
api_GetROP2% (hdc%) : "GetROP2"
api_GetStockObject% (nIndex%) : "GetStockObject"
api_GetStretchBltMode% (hdc%) : "GetStretchBltMode"
api_GetSystemPaletteEntries% (hdc%, wStartIndex%, wNumEntries%, lpPaletteEntries*) : "GetSystemPaletteEntries"
api_GetSystemPaletteUse% (hdc%) : "GetSystemPaletteUse"
api_GetTextAlign% (hdc%) : "GetTextAlign"
api_GetTextCharacterExtra% (hdc%) : "GetTextCharacterExtraA"
api_GetTextCharset% (hdc%) : "GetTextCharset"
api_GetTextCharsetInfo% (hdc%, lpSig*, dwFlags%) : "GetTextCharsetInfo"
api_GetTextColor% (hdc%) : "GetTextColor"
api_GetTextExtentExPoint% (hdc%, lpszStr$, cchString%, nMaxExtent%, lpnFit%, alpDx%, lpSize*) : "GetTextExtentExPointA"
api_GetTextExtentPoint% (hdc%, lpszString$, cbString%, lpSize*) : "GetTextExtentPointA"
api_GetTextExtentPoint32% (hdc%, lpsz$, cbString%, lpSize*) : "GetTextExtentPoint32A"
api_GetTextFace% (hdc%, nCount%, lpFacename$) : "GetTextFaceA"
api_GetTextMetrics% (hdc%, lpMetrics*) : "GetTextMetricsA"
api_GetViewportExtEx% (hdc%, lpSize*) : "GetViewportExtEx"
api_GetViewportOrgEx% (hdc%, lpPoint*) : "GetViewportOrgEx"
api_GetWindowExtEx% (hdc%, lpSize*) : "GetWindowExtEx"
api_GetWindowOrgEx% (hdc%, lpPoint*) : "GetWindowOrgEx"
api_GetWinMetaFileBits% (hemf%, cbBuffer%, lpbBuffer%, fnMapMode%, hdcRef%) : "GetWinMetaFileBits"
api_GetWorldTransform% (hdc%, lpXform*) : "GetWorldTransform"
api_IntersectClipRect% (hdc%, X1%, Y1%, X2%, Y2%) : "IntersectClipRect"
api_InvertRgn% (hdc%, hRgn%) : "InvertRgn"
api_LineDDA% (n1%, n2%, n3%, n4%, lpLineDDAProc%, lParam%) : "LineDDA"
api_LineTo% (hdc%, x%, y%) : "LineTo"
api_LPtoDP% (hdc%, lpPoint*, nCount%) : "LPtoDP"
api_MaskBlt% (hdcDest%, nXDest%, nYDest%, nWidth%, nHeight%, hdcSrc%, nXSrc%, nYSrc%, hbmMask%, xMask%, yMask%, dwRop%) : "MaskBlt"
api_ModifyWorldTransform% (hdc%, lpXform*, iMode%) : "ModifyWorldTransform"
api_MoveToEx% (hdc%, x%, y%, lpPoint*) : "MoveToEx"
api_OffsetClipRgn% (hdc%, x%, y%) : "OffsetClipRgn"
api_OffsetRgn% (hRgn%, x%, y%) : "OffsetRgn"
api_OffsetViewportOrgEx% (hdc%, nX%, nY%, lpPoint*) : "OffsetViewportOrgEx"
api_OffsetWindowOrgEx% (hdc%, nX%, nY%, lpPoint*) : "OffsetWindowOrgEx"
api_PaintRgn% (hdc%, hRgn%) : "PaintRgn"
api_PatBlt% (hdc%, x%, y%, nWidth%, nHeight%, dwRop%) : "PatBlt"
api_PathToRegion% (hdc%) : "PathToRegion"
api_Pie% (hdc%, X1%, Y1%, X2%, Y2%, X3%, Y3%, X4%, Y4%) : "Pie"
api_PlayEnhMetaFile% (hdc%, hemf%, lpRect*) : "PlayEnhMetaFile"
api_PlayEnhMetaFileRecord% (hdc%, lpHandletable*, lpEnhMetaRecord*, nHandles%) : "PlayEnhMetaFileRecord"
api_PlayMetaFile% (hdc%, hMF%) : "PlayMetaFile"
api_PlayMetaFileRecord% (hdc%, lpHandletable*, lpMetaRecord*, nHandles%) : "PlayMetaFileRecord"
api_PlgBlt% (hdcDest%, lpPoint*, hdcSrc%, nXSrc%, nYSrc%, nWidth%, nHeight%, hbmMask%, xMask%, yMask%) : "PlgBlt"
api_PolyBezier% (hdc%, lppt*, cPoints%) : "PolyBezier"
api_PolyBezierTo% (hdc%, lppt*, cCount%) : "PolyBezierTo"
api_PolyDraw% (hdc%, lppt*, lpbTypes%, cCount%) : "PolyDraw"
api_Polygon% (hdc%, lpPoint*, nCount%) : "Polygon"
api_Polyline% (hdc%, lpPoint*, nCount%) : "Polyline"
api_PolylineTo% (hdc%, lppt*, cCount%) : "PolylineTo"
api_PolyPolygon% (hdc%, lpPoint*, lpPolyCounts%, nCount%) : "PolyPolygon"
api_PolyPolyline% (hdc%, lppt*, lpdwPolyPoints%, cCount%) : "PolyPolyline"
api_PolyTextOut% (hdc%, pptxt*, cStrings%) : "PolyTextOutA"
api_PtInRegion% (hRgn%, x%, y%) : "PtInRegion"
api_PtVisible% (hdc%, x%, y%) : "PtVisible"
api_RealizePalette% (hdc%) : "RealizePalette"
api_Rectangle% (hdc%, X1%, Y1%, X2%, Y2%) : "Rectangle"
api_RectInRegion% (hRgn%, lpRect*) : "RectInRegion"
api_RectVisible% (hdc%, lpRect*) : "RectVisible"
api_RemoveFontResource% (lpFileName$) : "RemoveFontResourceA"
api_ResetDC% (hdc%, lpInitData*) : "ResetDCA"
api_ResizePalette% (hPalette%, nNumEntries%) : "ResizePalette"
api_RestoreDC% (hdc%, nSavedDC%) : "RestoreDC"
api_RoundRect% (hdc%, X1%, Y1%, X2%, Y2%, X3%, Y3%) : "RoundRect"
api_SaveDC% (hdc%) : "SaveDC"
api_ScaleViewportExtEx% (hdc%, nXnum%, nXdenom%, nYnum%, nYdenom%, lpSize*) : "ScaleViewportExtEx"
api_ScaleWindowExtEx% (hdc%, nXnum%, nXdenom%, nYnum%, nYdenom%, lpSize*) : "ScaleWindowExtEx"
api_SelectClipPath% (hdc%, iMode%) : "SelectClipPath"
api_SelectClipRgn% (hdc%, hRgn%) : "SelectClipRgn"
api_SelectObject% (hdc%, hObject%) : "SelectObject"
api_SelectPalette% (hdc%, hPalette%, bForceBackground%) : "SelectPalette"
api_SetAbortProc% (hDC%, lpAbortProc%) : "SetAbortProc"
api_SetArcDirection% (hdc%, ArcDirection%) : "SetArcDirection"
api_SetBitmapBits% (hBitmap%, dwCount%, lpBits*) : "SetBitmapBits"
api_SetBitmapDimensionEx% (hbm%, nX%, nY%, lpSize*) : "SetBitmapDimensionEx"
api_SetBkColor% (hdc%, crColor%) : "SetBkColor"
api_SetBkMode% (hdc%, nBkMode%) : "SetBkMode"
api_SetBoundsRect% (hdc%, lprcBounds*, flags%) : "SetBoundsRect"
api_SetBrushOrgEx% (hdc%, nXOrg%, nYOrg%, lppt*) : "SetBrushOrgEx"
api_SetColorAdjustment% (hdc%, lpca*) : "SetColorAdjustment"
api_SetColorSpace% (hdc%, hcolorspace%) : "SetColorSpace"
api_SetDeviceGammaRamp% (hdc%, lpv*) : "SetDeviceGammaRamp"
api_SetDIBColorTable% (hDC%, un1%, un2%, pcRGBQuad*) : "SetDIBColorTable"
api_SetDIBits% (hdc%, hBitmap%, nStartScan%, nNumScans%, lpBits*, lpBI*, wUsage%) : "SetDIBits"
api_SetDIBitsToDevice% (hdc%, x%, y%, dx%, dy%, SrcX%, SrcY%, Scan%, NumScans%, Bits*, BitsInfo*, wUsage%) : "SetDIBitsToDevice"
api_SetEnhMetaFileBits% (cbBuffer%, lpData%) : "SetEnhMetaFileBits"
api_SetGraphicsMode% (hdc%, iMode%) : "SetGraphicsMode"
api_SetICMMode% (hdc%, n%) : "SetICMMode"
api_SetICMProfile% (hdc%, lpStr$) : "SetICMProfileA"
api_SetMapMode% (hdc%, nMapMode%) : "SetMapMode"
api_SetMapperFlags% (hdc%, dwFlag%) : "SetMapperFlags"
api_SetMetaFileBitsEx% (nSize%, lpData%) : "SetMetaFileBitsEx"
api_SetMetaRgn% (hdc%) : "SetMetaRgn"
api_SetMiterLimit% (hdc%, eNewLimit*, peOldLimit*) : "SetMiterLimit"
api_SetPaletteEntries% (hPalette%, wStartIndex%, wNumEntries%, lpPaletteEntries*) : "SetPaletteEntries"
api_SetPixel% (hdc%, x%, y%, crColor%) : "SetPixel"
api_SetPixelFormat% (hDC%, n%, pcPixelFormatDescriptor*) : "SetPixelFormat"
api_SetPixelV% (hdc%, x%, y%, crColor%) : "SetPixelV"
api_SetPolyFillMode% (hdc%, nPolyFillMode%) : "SetPolyFillMode"
api_SetRectRgn% (hRgn%, X1%, Y1%, X2%, Y2%) : "SetRectRgn"
api_SetROP2% (hdc%, nDrawMode%) : "SetROP2"
api_SetStretchBltMode% (hdc%, nStretchMode%) : "SetStretchBltMode"
api_SetSystemPaletteUse% (hdc%, wUsage%) : "SetSystemPaletteUse"
api_SetTextAlign% (hdc%, wFlags%) : "SetTextAlign"
api_SetTextCharacterExtra% (hdc%, nCharExtra%) : "SetTextCharacterExtraA"
api_SetTextColor% (hdc%, crColor%) : "SetTextColor"
api_SetTextJustification% (hdc%, nBreakExtra%, nBreakCount%) : "SetTextJustification"
api_SetViewportExtEx% (hdc%, nX%, nY%, lpSize*) : "SetViewportExtEx"
api_SetViewportOrgEx% (hdc%, nX%, nY%, lpPoint*) : "SetViewportOrgEx"
api_SetWindowExtEx% (hdc%, nX%, nY%, lpSize*) : "SetWindowExtEx"
api_SetWindowOrgEx% (hdc%, nX%, nY%, lpPoint*) : "SetWindowOrgEx"
api_SetWinMetaFileBits% (cbBuffer%, lpbBuffer%, hdcRef%, lpmfp*) : "SetWinMetaFileBits"
api_SetWorldTransform% (hdc%, lpXform*) : "SetWorldTransform"
api_StartDoc% (hdc%, lpdi*) : "StartDocA"
api_StartPage% (hdc%) : "StartPage"
api_StretchBlt% (hdc%, x%, y%, nWidth%, nHeight%, hSrcDC%, xSrc%, ySrc%, nSrcWidth%, nSrcHeight%, dwRop%) : "StretchBlt"
api_StretchDIBits% (hdc%, x%, y%, dx%, dy%, SrcX%, SrcY%, wSrcWidth%, wSrcHeight%, lpBits*, lpBitsInfo*, wUsage%, dwRop%) : "StretchDIBits"
api_StrokeAndFillPath% (hdc%) : "StrokeAndFillPath"
api_StrokePath% (hdc%) : "StrokePath"
api_SwapBuffers% (hDC%) : "SwapBuffers"
api_TextOut% (hdc%, x%, y%, lpString$, nCount%) : "TextOutA"
api_TranslateCharsetInfo% (lpSrc%, lpcs*, dwFlags%) : "TranslateCharsetInfo"
api_UnrealizeObject% (hObject%) : "UnrealizeObject"
api_UpdateColors% (hdc%) : "UpdateColors"
api_WidenPath% (hdc%) : "WidenPath"