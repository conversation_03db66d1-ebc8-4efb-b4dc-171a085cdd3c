xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
 <b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}

template AnimTicksPerSecond {
 <9e415a43-7ba6-4a73-8743-b73d47e88476>
 DWORD AnimTicksPerSecond;
}


AnimTicksPerSecond {
 24;
}

Frame World {
 

 FrameTransformMatrix {
  1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000;;
 }
}

Mesh Mesh {
 36;
 -9.000000;7.142900;-10.276900;,
 -9.000000;14.285700;-7.176900;,
 -4.784958;16.262415;4.293081;,
 -9.000000;14.285700;8.900000;,
 -9.000000;7.142900;12.000000;,
 -6.000000;0.000000;0.861600;,
 9.000000;0.000000;-8.784600;,
 9.000000;0.000000;10.507700;,
 12.000000;10.000000;7.292300;,
 9.000000;20.000000;0.861600;,
 10.500000;17.857100;-12.000000;,
 10.500000;6.428600;-12.000000;,
 -6.000000;0.000000;0.861600;,
 -9.000000;7.142900;12.000000;,
 9.000000;0.000000;10.507700;,
 9.000000;0.000000;-8.784600;,
 -9.000000;7.142900;12.000000;,
 -9.000000;14.285700;8.900000;,
 12.000000;10.000000;7.292300;,
 9.000000;0.000000;10.507700;,
 -9.000000;14.285700;8.900000;,
 -4.784958;16.262415;4.293081;,
 9.000000;20.000000;0.861600;,
 12.000000;10.000000;7.292300;,
 -4.784958;16.262415;4.293081;,
 -9.000000;14.285700;-7.176900;,
 10.500000;17.857100;-12.000000;,
 9.000000;20.000000;0.861600;,
 -9.000000;14.285700;-7.176900;,
 -9.000000;7.142900;-10.276900;,
 10.500000;6.428600;-12.000000;,
 10.500000;17.857100;-12.000000;,
 -9.000000;7.142900;-10.276900;,
 -6.000000;0.000000;0.861600;,
 9.000000;0.000000;-8.784600;,
 10.500000;6.428600;-12.000000;;
 20;
 3;5,4,3;,
 3;5,3,2;,
 3;5,2,1;,
 3;5,1,0;,
 3;11,10,9;,
 3;11,9,8;,
 3;11,8,7;,
 3;11,7,6;,
 3;15,14,13;,
 3;15,13,12;,
 3;19,18,17;,
 3;19,17,16;,
 3;23,22,21;,
 3;23,21,20;,
 3;27,26,25;,
 3;27,25,24;,
 3;31,30,29;,
 3;31,29,28;,
 3;35,34,33;,
 3;35,33,32;;

 MeshNormals {
  36;
  -0.873534;-0.446517;0.193807;,
  -0.873534;-0.446517;0.193807;,
  -0.873534;-0.446517;0.193807;,
  -0.873534;-0.446517;0.193807;,
  -0.873534;-0.446517;0.193807;,
  -0.873534;-0.446517;0.193807;,
  0.957837;0.000000;-0.287311;,
  0.957837;0.000000;-0.287311;,
  0.957837;0.000000;-0.287311;,
  0.957837;0.000000;-0.287311;,
  0.957837;0.000000;-0.287311;,
  0.957837;0.000000;-0.287311;,
  -0.294705;0.458307;-0.838513;,
  -0.294705;0.458307;-0.838513;,
  -0.294705;0.458307;-0.838513;,
  -0.294705;0.458307;-0.838513;,
  0.149799;0.906995;0.393598;,
  0.149799;0.906995;0.393598;,
  0.149799;0.906995;0.393598;,
  0.149799;0.906995;0.393598;,
  -0.028700;0.535709;0.843915;,
  -0.028700;0.535709;0.843915;,
  -0.028700;0.535709;0.843915;,
  -0.028700;0.535709;0.843915;,
  -0.283394;-0.580589;0.763285;,
  -0.283394;-0.580589;0.763285;,
  -0.283394;-0.580589;0.763285;,
  -0.283394;-0.580589;0.763285;,
  -0.066302;-0.915329;0.397212;,
  -0.066302;-0.915329;0.397212;,
  -0.066302;-0.915329;0.397212;,
  -0.066302;-0.915329;0.397212;,
  -0.294705;-0.458307;-0.838513;,
  -0.294705;-0.458307;-0.838513;,
  -0.294705;-0.458307;-0.838513;,
  -0.294705;-0.458307;-0.838513;;
  20;
  3;5,4,3;,
  3;5,3,2;,
  3;5,2,1;,
  3;5,1,0;,
  3;11,10,9;,
  3;11,9,8;,
  3;11,8,7;,
  3;11,7,6;,
  3;15,14,13;,
  3;15,13,12;,
  3;19,18,17;,
  3;19,17,16;,
  3;23,22,21;,
  3;23,21,20;,
  3;27,26,25;,
  3;27,25,24;,
  3;31,30,29;,
  3;31,29,28;,
  3;35,34,33;,
  3;35,33,32;;
 }

 MeshTextureCoords {
  36;
  0.896300;-0.055800;,
  0.920500;-0.111600;,
  0.983300;-0.150700;,
  1.046100;-0.111600;,
  1.070300;-0.055800;,
  0.983300;0.000000;,
  0.759500;0.000000;,
  0.910200;0.000000;,
  0.885100;-0.078100;,
  0.834900;-0.156300;,
  0.734400;-0.139500;,
  0.734400;-0.050200;,
  0.593800;-0.202000;,
  0.570300;-0.289100;,
  0.710900;-0.277400;,
  0.710900;-0.126700;,
  0.570300;-0.055800;,
  0.570300;-0.111600;,
  0.734400;-0.078100;,
  0.710900;0.000000;,
  0.570300;-0.108600;,
  0.546900;-0.045800;,
  0.710900;-0.045800;,
  0.734400;-0.096000;,
  1.429700;-0.100500;,
  1.453100;-0.037700;,
  1.605500;0.000000;,
  1.593800;-0.100500;,
  0.539100;-0.096000;,
  0.539100;-0.040200;,
  0.691400;-0.034600;,
  0.691400;-0.123900;,
  0.570300;-0.013500;,
  0.593800;-0.100500;,
  0.710900;-0.025100;,
  0.722700;0.000000;;
 }

 VertexDuplicationIndices {
  36;
  36;
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
  32,
  33,
  34,
  35;
 }

 MeshMaterialList {
  1;
  20;
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0;

  Material material_1 {
   1.000000;1.000000;1.000000;1.000000;;
   50.000000;
   1.000000;1.000000;1.000000;;
   0.000000;0.000000;0.000000;;

   TextureFilename {
    "crtrec.jpg";
   }
  }
 }

 XSkinMeshHeader {
  1;
  1;
  1;
 }

 SkinWeights {
  "World";
  36;
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
  32,
  33,
  34,
  35;
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000,
  1.000000;
  1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000,0.000000,0.000000,0.000000,0.000000,1.000000;;
 }
}