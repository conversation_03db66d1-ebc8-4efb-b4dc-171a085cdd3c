xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
<b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}


Frame Scene_Root {


 FrameTransformMatrix {
  1.000000, 0.000000, 0.000000, 0.000000,
  0.000000, 1.000000, 0.000000, 0.000000,
  0.000000, 0.000000, 1.000000, 0.000000,
  0.000000, 0.000000, 0.000000, 1.000000;;
 }

  Frame default {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    46;
    -19.501989;0.000000;-7.942614;,
    19.501989;0.000000;-7.942614;,
    19.502087;1.068304;-7.852581;,
    9.044209;1.068543;-7.856939;,
    -19.496677;1.074062;-7.855243;,
    -17.500502;2.131331;8.484375;,
    17.500504;2.182064;8.483173;,
    17.500504;0.005232;8.533376;,
    -17.500504;0.005232;8.533376;,
    17.500504;0.005232;-6.966673;,
    -17.500504;0.005232;-6.966673;,
    -19.501987;2.304516;9.454710;,
    -19.501989;0.000000;-7.942614;,
    -19.501869;3.615444;9.422180;,
    9.044209;3.615754;9.423641;,
    19.494366;3.619088;9.420719;,
    19.501989;2.361051;9.453371;,
    -19.501987;2.304516;9.454710;,
    19.501989;0.000000;-7.942614;,
    19.501989;2.361051;9.453371;,
    -19.501987;2.304516;9.454710;,
    19.501989;2.361051;9.453371;,
    17.500504;2.182064;8.483173;,
    -17.500502;2.131331;8.484375;,
    19.501989;0.000000;-7.942614;,
    17.500504;0.005232;-6.966673;,
    -19.501989;0.000000;-7.942614;,
    -17.500504;0.005232;-6.966673;,
    17.500504;0.005232;8.533376;,
    -17.500504;0.005232;8.533376;,
    -17.713167;3.198911;6.339340;,
    7.238125;3.196135;6.318800;,
    10.850294;3.195733;6.315826;,
    -17.009378;1.958772;-4.948955;,
    6.496629;1.958772;-4.948955;,
    6.496629;3.588293;5.846525;,
    -16.973909;3.590904;5.865847;,
    7.238125;1.474564;-5.157764;,
    10.850294;1.474564;-5.157764;,
    18.548426;1.474564;-5.157764;,
    18.541891;3.194877;6.309494;,
    -17.750874;1.474565;-5.157764;,
    11.155430;1.982543;-4.874333;,
    18.243290;1.982543;-4.874333;,
    18.237274;3.579995;5.683856;,
    11.155430;3.580783;5.689686;;
    60;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;5,7,6;,
    3;5,8,7;,
    3;9,6,7;,
    3;8,5,10;,
    3;11,4,12;,
    3;11,13,4;,
    3;13,15,14;,
    3;13,16,15;,
    3;13,17,16;,
    3;18,15,19;,
    3;18,2,15;,
    3;20,22,21;,
    3;20,23,22;,
    3;21,25,24;,
    3;21,22,25;,
    3;26,23,20;,
    3;26,27,23;,
    3;26,25,27;,
    3;26,24,25;,
    3;28,27,25;,
    3;28,29,27;,
    3;30,14,31;,
    3;30,13,14;,
    3;31,14,32;,
    3;33,35,34;,
    3;33,36,35;,
    3;37,32,38;,
    3;37,31,32;,
    3;39,15,2;,
    3;39,40,15;,
    3;4,37,3;,
    3;4,41,37;,
    3;3,37,38;,
    3;4,30,41;,
    3;4,13,30;,
    3;32,15,40;,
    3;32,14,15;,
    3;42,44,43;,
    3;42,45,44;,
    3;3,39,2;,
    3;3,38,39;,
    3;41,34,37;,
    3;41,33,34;,
    3;37,35,31;,
    3;37,34,35;,
    3;31,36,30;,
    3;31,35,36;,
    3;30,33,41;,
    3;30,36,33;,
    3;38,43,39;,
    3;38,42,43;,
    3;39,44,40;,
    3;39,43,44;,
    3;40,45,32;,
    3;40,44,45;,
    3;32,42,38;,
    3;32,45,42;;

    MeshNormals {
     46;
     0.000125;0.077968;-0.996956;,
     0.000000;0.083979;-0.996468;,
     0.307114;0.648463;-0.696546;,
     0.000132;0.845802;-0.533497;,
     -0.421408;0.844145;-0.331412;,
     -0.447213;0.020616;0.894190;,
     0.707107;0.016303;0.706918;,
     0.447214;0.020615;0.894189;,
     -0.707107;0.016293;0.706919;,
     1.000000;0.000000;-0.000000;,
     -1.000000;0.000001;-0.000000;,
     -0.999997;0.002541;-0.000487;,
     -0.999987;0.004999;-0.000662;,
     -0.522970;0.665938;0.532005;,
     -0.000091;0.935809;-0.352507;,
     0.531785;0.844480;-0.063700;,
     0.000017;0.025376;0.999678;,
     -0.000002;0.024807;0.999692;,
     0.999996;0.002954;-0.000177;,
     0.999981;0.006039;-0.000820;,
     -0.006417;-0.986629;0.162856;,
     0.028420;-0.988150;0.150839;,
     0.007929;-0.986165;0.165575;,
     -0.028159;-0.988479;0.148718;,
     0.031514;-0.997051;0.069974;,
     0.016797;-0.998231;0.057027;,
     -0.021517;-0.997337;0.069680;,
     -0.015971;-0.999245;0.035424;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     -0.163086;0.986250;-0.026720;,
     0.097024;0.986050;0.135250;,
     -0.236078;0.970560;-0.047760;,
     -0.234938;0.914180;-0.330272;,
     0.151499;0.770178;-0.619576;,
     0.228831;0.973284;0.018825;,
     -0.147700;0.947997;0.281933;,
     0.141654;0.955164;-0.259993;,
     -0.163445;0.879989;-0.445987;,
     0.287253;0.918813;-0.270681;,
     0.203020;0.966513;0.156956;,
     -0.119974;0.842226;-0.525605;,
     -0.404221;0.855981;-0.322339;,
     0.252353;0.759926;-0.599025;,
     0.393597;0.919165;0.014756;,
     -0.244659;0.939917;0.238114;;
     60;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;5,7,6;,
     3;5,8,7;,
     3;9,6,7;,
     3;8,5,10;,
     3;11,4,12;,
     3;11,13,4;,
     3;13,15,14;,
     3;13,16,15;,
     3;13,17,16;,
     3;18,15,19;,
     3;18,2,15;,
     3;20,22,21;,
     3;20,23,22;,
     3;21,25,24;,
     3;21,22,25;,
     3;26,23,20;,
     3;26,27,23;,
     3;26,25,27;,
     3;26,24,25;,
     3;28,27,25;,
     3;28,29,27;,
     3;30,14,31;,
     3;30,13,14;,
     3;31,14,32;,
     3;33,35,34;,
     3;33,36,35;,
     3;37,32,38;,
     3;37,31,32;,
     3;39,15,2;,
     3;39,40,15;,
     3;4,37,3;,
     3;4,41,37;,
     3;3,37,38;,
     3;4,30,41;,
     3;4,13,30;,
     3;32,15,40;,
     3;32,14,15;,
     3;42,44,43;,
     3;42,45,44;,
     3;3,39,2;,
     3;3,38,39;,
     3;41,34,37;,
     3;41,33,34;,
     3;37,35,31;,
     3;37,34,35;,
     3;31,36,30;,
     3;31,35,36;,
     3;30,33,41;,
     3;30,36,33;,
     3;38,43,39;,
     3;38,42,43;,
     3;39,44,40;,
     3;39,43,44;,
     3;40,45,32;,
     3;40,44,45;,
     3;32,42,38;,
     3;32,45,42;;
    }

    MeshTextureCoords {
     46;
     0.045456;0.503439;,
     0.955715;0.503675;,
     0.955692;0.481025;,
     0.711687;0.481105;,
     0.045569;0.480724;,
     0.757694;0.957323;,
     0.244196;0.956578;,
     0.244196;0.988519;,
     0.757694;0.988519;,
     0.016856;0.983278;,
     0.985035;0.983279;,
     0.009427;0.046748;,
     0.016483;0.486408;,
     0.045536;0.042365;,
     0.711757;0.042055;,
     0.955843;0.042196;,
     0.955850;0.015537;,
     0.045592;0.014646;,
     0.984592;0.486819;,
     0.990636;0.046397;,
     0.977990;0.517062;,
     0.015536;0.517095;,
     0.064924;0.541036;,
     0.928602;0.541006;,
     0.015536;0.946355;,
     0.064924;0.922273;,
     0.977991;0.946355;,
     0.928602;0.922273;,
     0.064924;0.539797;,
     0.928602;0.539797;,
     0.087236;0.120227;,
     0.669561;0.120748;,
     0.753864;0.120823;,
     0.103662;0.405406;,
     0.652256;0.405406;,
     0.652256;0.130895;,
     0.104489;0.130405;,
     0.669561;0.412535;,
     0.753864;0.412535;,
     0.933526;0.412535;,
     0.933374;0.120984;,
     0.086356;0.412535;,
     0.760985;0.403459;,
     0.926405;0.403459;,
     0.926265;0.134966;,
     0.760985;0.134818;;
    }

    VertexDuplicationIndices {
     46;
     46;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31,
     32,
     33,
     34,
     35,
     36,
     37,
     38,
     39,
     40,
     41,
     42,
     43,
     44,
     45;
    }

    MeshMaterialList {
     1;
     60;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material Material02 {
      0.800000;0.800000;0.800000;1.000000;;
      0.000000;
      0.000000;0.000000;0.000000;;
      0.000000;0.000000;0.000000;;
      TextureFilename {
       "keyboard.jpg";
      }
     }
    }

   }
 }
 }
}
