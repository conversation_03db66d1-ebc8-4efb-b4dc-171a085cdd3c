.lib "FreeImage.dll"

FI_Allocate%( Width%,Height%,BPP%,RedMask%,GreenMask%,BlueMask% ) : "_FreeImage_Allocate@24"
FI_Load%( ImageType%,File$,Mode% ) : "_FreeImage_Load@12"
FI_Save%( ImapeType%,FIBITMAP%,File$,Flags% ) : "_FreeImage_Save@16"
FI_Unload%( FIBITMAP% ) : "_FreeImage_Unload@4"
FI_GetFIFDescription$( FREE_IMAGE_FORMAT% ) : "_FreeImage_GetFIFDescription@4"
FI_GetFileType%( File$,Default% ) : "_FreeImage_GetFileType@8"
FI_ConvertToRawBits( Bank*,FIBITMAP%,Pitch%,Depth%,Red_Mask%,Green_Mask%,Blue_Mask%,TopDown% ) : "_FreeImage_ConvertToRawBits@32"
FI_ConvertToRawBits3D( Pointer,FIBITMAP%,Pitch%,Depth%,Red_Mask%,Green_Mask%,<PERSON>_Mask%,TopDown% ) : "_FreeImage_ConvertToRawBits@32"
FI_GetFIFFromFilename%( File$ ) : "_FreeImage_GetFIFFromFilename@4"
FI_GetColorsUsed%( FIBITMAP% ) : "_FreeImage_GetColorsUsed@4"
FI_GetBits%( FIBITMAP% ) : "_FreeImage_GetBits@4"
FI_GetLine%( FIBITMAP%,ScanLine% ) : "_FreeImage_GetLine@8"
FI_GetBPP%( FIBITMAP% ) : "_FreeImage_GetBPP@4"
FI_GetWidth%( FIBITMAP% ) : "_FreeImage_GetWidth@4"
FI_GetHeight%( FIBITMAP% ) : "_FreeImage_GetHeight@4"
FI_GetPitch%( FIBITMAP% ) : "_FreeImage_GetPitch@4"
FI_GetDIBSize%( FIBITMAP% ) : "_FreeImage_GetDIBSize@4"
FI_GetPalette%( FIBITMAP% ) : "_FreeImage_GetPalette@4"
FI_GetDotsPerMeterX%( FIBITMAP% ) : "_FreeImage_GetDotsPerMeterX@4"
FI_GetDotsPerMeterY%( FIBITMAP% ) : "_FreeImage_GetDotsPerMeterY@4"
FI_GetInfoHeader%( FIBITMAP% ) : "_FreeImage_GetInfoHeader@4"
FI_GetInfo%( FIBITMAP% ) : "_FreeImage_GetInfo@4"
FI_GetColorType%( FIBITMAP% ) : "_FreeImage_GetColorType@4"
FI_GetRedMask%( FIBITMAP% ) : "_FreeImage_GetRedMask@4"
FI_GetGreenMask%( FIBITMAP% ) : "_FreeImage_GetGreenMask@4"
FI_GetBlueMask%( FIBITMAP% ) : "_FreeImage_GetBlueMask@4"
FI_GetTransparencyCount%( FIBITMAP% ) : "_FreeImage_GetTransparencyCount@4"
FI_GetTransparencyTable%( FIBITMAP% ) : "_FreeImage_GetTransparencyTable@4"
FI_SetTransparencyTable%( FIBITMAP%,Table*,Size ) : "_FreeImage_SetTransparencyTable@12"
FI_SetTransparent( FIBITMAP%,Flag% ) : "_FreeImage_SetTransparent@8"
FI_IsTransparent%( FIBITMAP% ) : "_FreeImage_IsTransparent@4"
FI_ConvertTo8Bits%( FIBITMAP% ) : "_FreeImage_ConvertTo8Bits@4"
FI_ConvertTo16Bits555%( FIBITMAP% ) : "_FreeImage_ConvertTo16Bits555@4"
FI_ConvertTo16Bits565%( FIBITMAP% ) : "_FreeImage_ConvertTo16Bits565@4"
FI_ConvertTo24Bits%( FIBITMAP% ) : "_FreeImage_ConvertTo24Bits@4"
FI_ConvertTo32Bits%( FIBITMAP% ) : "_FreeImage_ConvertTo32Bits@4"
FI_ColorQuantize%( FIBITMAP%,Flag% ) : "_FreeImage_ColorQuantize@8"
FI_Threshold%( FIBITMAP%,Flag% ) : "_FreeImage_Threshold@8"
FI_Dither%( FIBITMAP%,Algo% ) : "_FreeImage_Dither@8"
FI_ConvertFromRawBits%( Bank*,Width%,Height%,Pitch%,Depth%,RedMask%,GreenMask%,BlueMask%,TopDown% ) : "_FreeImage_ConvertFromRawBits@36"
FI_ConvertFromRawBits3D%( Pointer,Width%,Height%,Pitch%,Depth%,RedMask%,GreenMask%,BlueMask%,TopDown% ) : "_FreeImage_ConvertFromRawBits@36"
FI_Clone%(DIB%) : "_FreeImage_Clone@4"
FI_FIFSupportsReading%( FREE_IMAGE_FORMAT% ) : "_FreeImage_FIFSupportsReading@4"
FI_FIFSupportsWriting%( FREE_IMAGE_FORMAT% ) : "_FreeImage_FIFSupportsWriting@4"
FI_FIFSupportsExportBPP%( FREE_IMAGE_FORMAT%, BPP% ) : "_FreeImage_FIFSupportsExportBPP@8"
FI_ZLibCompress%( BankOut*,BSizeOut%,BankIn*,BSizeIn% ) : "_FreeImage_ZLibCompress@16"
FI_ZLibUncompress%( BankOut*,BSizeOut%,BankIn*,BSizeIn% ) : "_FreeImage_ZLibUncompress@16"
FI_RotateClassic%( FIBITMAP%,Angle% ) : "_FreeImage_RotateClassic@8"
FI_RotateEx%( FIBITMAP%,Angle%,Shiftx%,Shifty%,offx%,offy%,Masked% ) : "_FreeImage_RotateEx@28"
FI_FlipHorizontal%( FIBITMAP% ) : "_FreeImage_FlipHorizontal@4"
FI_FlipVertical%( FIBITMAP% ) : "_FreeImage_FlipVertical@4"
FI_Rescale%( FIBITMAP%,Width%,Height%,Filter% ) : "_FreeImage_Rescale@16"
FI_AdjustCurve%( FIBITMAP%,Bank*,Channel% ) : "_FreeImage_AdjustCurve@12"
FI_AdjustGamma%( FIBITMAP%,Gamma% ) : "_FreeImage_AdjustGamma@8"
FI_AdjustBrightness%( FIBITMAP%,Percent% ) : "_FreeImage_AdjustBrightness@8"
FI_AdjustContrast%( FIBITMAP%,Percent% ) : "_FreeImage_AdjustContrast@8"
FI_Invert%( FIBITMAP% ) : "_FreeImage_Invert@4"
FI_GetHistogram%( FIBITMAP%,Bank*,Channel% ) : "_FreeImage_GetHistogram@12"
FI_GetChannel%( FIBITMAP%,Channel% ) : "_FreeImage_GetChannel@8"
FI_SetChannel%( FIBITMAP%, FIBITMAP8%, Channel% ) : "_FreeImage_SetChannel@12"
FI_Copy%( FIBITMAP%,XA%,XB%,YA%,YB% ) : "_FreeImage_Copy@20"
FI_Paste%( FIBITMAP%,X%,Y%,Alpha% ) : "_FreeImage_Paste@16"
FI_GetVersion$() : "_FreeImage_GetVersion@0"

.lib "kernel32.dll"
RtlMoveMemory_To%(Destination%,Source*,Length%) : "RtlMoveMemory"
RtlMoveMemory_From%(Destination*,Source%,Length%) : "RtlMoveMemory" 
RtlMoveMemory_Ex%(Destination%,Source%,Length%) : "RtlMoveMemory"