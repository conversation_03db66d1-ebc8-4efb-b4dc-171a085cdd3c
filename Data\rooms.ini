;=== STYLE ABBREVIATIONS ===
;LCZ - Light Containment Zone
;HCZ - Heavy Containment Zone
;EZ - Entrance Zone
;===========================

[room ambience]
ambience1 = SFX\Ambient\Room ambience\rumble.ogg
ambience2 = SFX\Ambient\Room ambience\lowdrone.ogg
ambience3 = SFX\Ambient\Room ambience\pulsing.ogg
ambience4 = SFX\Ambient\Room ambience\ventilation.ogg
ambience5 = SFX\Ambient\Room ambience\drip.ogg
ambience6 = SFX\Alarm\Alarm.ogg
ambience7 = SFX\Ambient\Room ambience\895.ogg
ambience8 = SFX\Ambient\Room ambience\fuelpump.ogg
ambience9 = SFX\Ambient\Room ambience\Fan.ogg
ambience10 = SFX\Ambient\Room ambience\servers1.ogg

;LIGHT CONTAINMENT ZONE ROOMS
;============================
[lockroom]
descr=A timed airlock room, with two doors.
mesh path=GFX\map\lockroom_opt.rmesh
shape = 2C
commonness = 30
zone1=1
zone2=3

[173]
descr=Class-D Cells & SCP-173's chamber in the intro. Placed automatically in all maps.
mesh path=GFX\map\173bright_opt.rmesh
shape = 1
commonness = 0
disabledecals = true

[start]
descr=SCP-173's chamber, after the breach. Placed automatically in all maps.
mesh path=GFX\map\173_opt.rmesh
shape = 1
commonness = 0
disabledecals = true

[room1123]
descr=SCP-1123's containment chamber.
mesh path=GFX\map\1123_opt.rmesh
shape = 2
commonness = 0
zone1=1
disabledecals = true

[room1archive]
descr=An archive room, where various (random) items spawn on shelves.
mesh path=GFX\map\room1archive_opt.rmesh
shape = 1
commonness = 80
zone1=1

[room2storage]
descr=SCP-970's storage hallway.
mesh path=GFX\map\room2storage_opt.rmesh
shape = 2
commonness = 0
zone1=1
disabledecals = true

[room3storage]
descr=SCP-939's storage area, with various roaming instances of 939.
mesh path=GFX\map\room3storage_opt.rmesh
shape = 3
commonness = 0
zone1=1
disableoverlapcheck=true

[room2tesla_lcz]
descr=Hallway with a tesla gate. LCZ variant.
mesh path=GFX\map\room2tesla_lcz_opt.rmesh
shape = 2
commonness = 100
zone1=1

[endroom]
descr=Red-lit dead end room with a large, unopenable gate.
mesh path=GFX\map\endroom_opt.rmesh
shape = 1
commonness = 100
zone1=1
zone3=3

[room012]
descr=SCP-012's containment chamber.
mesh path=GFX\map\room012_opt.rmesh
shape = 2
commonness = 0
zone1=1
disabledecals = true

[room205]
descr=SCP-205's containment chamber.
mesh path=GFX\map\room205_opt.rmesh
shape = 1
commonness = 0
zone1=1
large = true

[room2]
descr=An empty, two-door hallway.
mesh path=GFX\map\room2_opt.rmesh
shape = 2
commonness = 45
zone1=1

[room2_2]
descr=An empty, two-door hallway with a large fan/vent in the wall.
mesh path=GFX\map\room2_2_opt.rmesh
shape = 2
commonness = 40
zone1=1

[room2_3]
descr=A larger version of the two-door hallway, with lights in the floor.
mesh path=GFX\map\room2_3_opt.rmesh
shape = 2
commonness = 35
zone1=1

[room2_4]
descr=A two-door hallway, with a unopenable door off to the side.
mesh path=GFX\map\room2_4_opt.rmesh
shape=2
commonness = 30
zone1=1

[room2_5]
descr=A two-door hallway, with lowered ceilings.
mesh path=GFX\map\room2_5_opt.rmesh
shape=2
commonness = 35
zone1=1

[room2C]
descr=An empty, plain corner room.
mesh path=GFX\map\room2C_opt.rmesh
shape = 2C
commonness = 40
zone1=1

[room2c2]
descr=A corner room, with more of a LCZ style.
mesh path=GFX\map\room2c2_opt.rmesh
shape = 2C
commonness = 30
zone1=1

[room2closets]
descr=A storage hallway, where SCP-173 kills two NPCs. The Gas Mask & Batteries spawn here.
mesh path=GFX\map\room2closets_opt.rmesh
shape = 2
commonness = 0
zone1=1
disabledecals  = true
large = true

[room2elevator]
descr=A two-door hallway, with an elevator off to the side.
mesh path=GFX\map\room2elevator_opt.rmesh
shape = 2
commonness = 20
zone1=1

[room2doors]
descr=Red-lit airlock room, in the shape of a T.
mesh path=GFX\map\room2doors_opt.rmesh
shape = 2
commonness = 30
zone1=1

[room2scps]
descr=SCP-714's, SCP-860's, & SCP-1025's two-door hallway.
mesh path=GFX\map\room2scps_opt.rmesh
shape = 2
commonness = 0
zone1=1

[room860]
descr=SCP-860-1's containment chamber. Can't be traversed without SCP-860.
mesh path=GFX\map\room860_opt.rmesh
shape = 2
commonness = 0

[room2testroom2]
descr=Two-door hallway with a small testing room, where SCP-173 shatters the testroom's window.
mesh path=GFX\map\room2testroom2_opt.rmesh
shape = 2
commonness = 0
zone1=1

[room3]
descr=Three-door hallway, with a caged off portion in the back wall.
mesh path=GFX\map\room3_opt.rmesh
shape = 3
commonness = 100
zone1=1

[room3_2]
descr=A three-door hallway, without the caged portion.
mesh path=GFX\map\room3_2_opt.rmesh
shape = 3
commonness = 100
zone1=1

[room4]
descr=A four-door hallway, with a metal walkway hanging above.
mesh path=GFX\map\room4_opt.rmesh
shape = 4
commonness = 100
zone1=1

[room4_2]
descr=A varient of the four-door hallway, without the metal walkway.
mesh path=GFX\map\room4_2_opt.rmesh
shape=4
commonness = 80
zone1=1

[roompj]
descr=SCP-372's containment chamber.
mesh path=GFX\map\roompj_opt.rmesh
shape = 1
commonness = 0
disabledecals = true
zone1=1

[914]
descr=SCP-914's containment chamber.
mesh path=GFX\map\machineroom_opt.rmesh
shape = 1
commonness = 0
zone1=1

[room2gw]
descr=A two-door contamination airlock.
mesh path=GFX\map\room2gw_opt.rmesh
shape=2
commonness = 10
zone1=1

[room2gw_b]
descr=Broken varient of the two-door contamination airlock.
mesh path=GFX\map\room2gw_b_opt.rmesh
shape=2
commonness = 0
zone1=1

[room1162]
descr=SCP-1162's containment chamber, in a corner room.
mesh path=GFX\map\room1162_opt.rmesh
shape=2c
commonness = 0
zone1=1

[room2scps2]
descr=SCP-1499 & SCP-500's containment chambers, in a two-door hallway.
mesh path=GFX\map\room2scps2_opt.rmesh
shape=2
commonness = 0
zone1=1

[room2sl]
descr=The surveillance room in the LCZ. Required for passing into HCZ, if used.
mesh path=GFX\map\room2sl_opt.rmesh
shape=2
commonness = 0
zone1=1
large = true

[lockroom3]
descr=A varient of the timed lockroom, where a seperate path is opened. The airlock itself is broken.
mesh path=GFX\map\lockroom3_opt.rmesh
shape=2c
commonness = 15
zone1=1

[room4info]
descr=A 4-way hallway containing an info center.
mesh path=GFX\map\room4info_opt.rmesh
shape=4
commonness = 0
zone1=1

[room3_3]
descr=Another variant of a three-door hallway.
mesh path=GFX\map\room3_3_opt.rmesh
shape=3
commonness = 20
zone1=1

[checkpoint1]
descr=The keycarded checkpoint between the LCZ and HCZ.
mesh path=GFX\map\checkpoint1_opt.rmesh
shape = 2
commonness = 0

;HEAVY CONTAINMENT ZONE ROOMS
;============================
[008]
descr=SCP-008's containment chamber.
mesh path=GFX\map\008_opt.rmesh
shape = 1
commonness = 0
disabledecals = true
zone1=2

[room035]
descr=SCP-035's containment chamber.
mesh path=GFX\map\room035_opt.rmesh
shape = 1
commonness = 0
zone1=2

[room049]
descr=SCP-049's containment chamber.
mesh path=GFX\map\room049_opt.rmesh
shape = 2
commonness = 0
disabledecals = true
walksound=1
zone1=2
usevolumelighting = 1
disableoverlapcheck=true

[room106]
descr=SCP-106's containment chamber.
mesh path=GFX\map\room106_opt.rmesh
shape = 1
commonness = 0
disabledecals = true
zone1=2
large = true

[room513]
descr=SCP-513's containment chamber.
mesh path=GFX\map\room513_opt.rmesh
shape = 3
commonness = 0
zone1=2
usevolumelighting = 1

[coffin]
descr=SCP-895's containment chamber.
mesh path=GFX\map\coffin_opt.rmesh
shape = 1
commonness = 0
disabledecals = true
walksound=1
zone1=2

[room966]
descr=SCP-966's containment chamber.
mesh path=GFX\map\room966_opt.rmesh
shape = 3
commonness = 0
zone1=2
usevolumelighting = 1
disableoverlapcheck=true

[endroom2]
descr=A small, red-lit, HCZ-styled endroom.
mesh path=GFX\map\endroom2_opt.rmesh
shape = 1
commonness = 100
zone1=2

[testroom]
descr=A large testroom, where SCP-682's document can be found.
mesh path=GFX\map\testroom_opt.rmesh
shape = 2
commonness = 0
walksound = 1
disabledecals = true
zone1=2

[tunnel]
descr=A two-door hallway, in the shape of a tunnel.
mesh path=GFX\map\tunnel_opt.rmesh
shape = 2
commonness = 100
walksound=1
zone1=2
usevolumelighting = 1

[tunnel2]
descr=A varient of the tunnel hallway, with a fuse box off to the side where SCP-173 spawns.
mesh path=GFX\map\tunnel2_opt.rmesh
shape = 2
commonness = 70
walksound=1
zone1=2
usevolumelighting = 1

[room2Ctunnel]
descr=A HCZ-styled corner room.
mesh path=GFX\map\room2Ctunnel_opt.rmesh
shape = 2C
commonness = 40
walksound=1
zone1=2
usevolumelighting = 1

[room2nuke]
descr=A two-door hallway, with a room on the side which leads to the Omega & Alpha warheads.
mesh path=GFX\map\room2nuke_opt.rmesh
shape = 2
commonness = 0
zone1=2
large = true

[room2pipes]
descr=A two-door hallway, with caged floors and ceiling that contain various pipes. SCP-106 can appear out of the walls.
mesh path=GFX\map\room2pipes_opt.rmesh
shape = 2
commonness = 50
disabledecals = true
walksound=1
zone1=2

[room2pit]
descr=A two-door catwalk, where the catwalk spews gas.
mesh path=GFX\map\room2pit_opt.rmesh
shape = 2
commonness = 75
disabledecals = true
walksound=1
zone1=2

[room3pit]
descr=A three-door walkway, with a large box-like structure in the back wall.
mesh path=GFX\map\room3pit_opt.rmesh
shape = 3
commonness = 100
disabledecals = true
walksound=1
zone1=2

[room4pit]
descr=A four-door walkway, with a large cube-like stucture in the middle of the room.
mesh path=GFX\map\room4pit_opt.rmesh
shape=4
commonness = 100
zone1=2

[room2servers]
descr=SCP-096's spawn area, where 096 kills a guard.
mesh path=GFX\map\room2servers_opt.rmesh
shape = 2
commonness = 0
walksound=1
zone1=2
large = true

[room2shaft]
descr=A two-door hallway, which has a non-functional elevator and the maintenance shaft.
mesh path=GFX\map\room2shaft_opt.rmesh
shape = 2
commonness = 0
disabledecals = true

[room2tunnel]
descr=A closed gate, and elevators leading to the maintenance tunnels. Gate can be opened if SCP-372's chamber is in the map for the code.
mesh path=GFX\map\room2tunnel_opt.rmesh
shape = 2
commonness = 0
disabledecals = true
walksound=1
zone1=2

[room3tunnel]
descr=A three-door hallway, shaped after tunnels.
mesh path=GFX\map\room3tunnel_opt.rmesh
shape = 3
commonness = 100
walksound=1
zone1=2
usevolumelighting = 1

[room4tunnels]
descr=A four-door hallway, shaped after tunnels.
mesh path=GFX\map\4tunnels_opt.rmesh
shape = 4
commonness = 100
walksound=1
zone1=2
usevolumelighting = 1

[room2tesla_hcz]
descr=Hallway with a tesla gate. HCZ variant.
mesh path=GFX\map\room2tesla_hcz_opt.rmesh
shape = 2
commonness = 100
zone1=2

[room3z2]
descr=A T-shaped, three-door hallway.
mesh path=GFX\map\room3z2_opt.rmesh
shape=3
commonness = 100
zone1=2

[room2cpit]
descr=A corner room, with a large cube-like stucture in the middle of the room.
mesh path=GFX\map\room2cpit_opt.rmesh
shape=2c
commonness = 0
disabledecals  = true
zone1=2

[room2pipes2]
descr=Another variant of the two-door hallway, with caged floors and ceiling that contain various pipes.
mesh path=GFX\map\room2pipes2_opt.rmesh
shape = 2
commonness = 70
disabledecals = true
zone1=2

[checkpoint2]
descr=The keycarded checkpoint which leads from the HCZ to the EZ.
mesh path=GFX\map\checkpoint2_opt.rmesh
shape = 2
commonness = 0

;ENTRANCE ZONE ROOMS
;============================
[room079]
descr=SCP-079's containment chamber.
mesh path=GFX\map\room079_opt.rmesh
shape = 1
commonness = 0
disabledecals = true
zone1=3
large = true

[lockroom2]
descr=The open-doored lockroom, with SCP-096 sitting in the middle.
mesh path=GFX\map\lockroom2_opt.rmesh
shape = 2C
commonness = 0
zone1=3

[exit1]
descr=The entrance room to Gate B, with an elevator to the surface of Gate B.
mesh path=GFX\map\exit1_opt.rmesh
shape = 1
commonness = 0
zone1=3
disableoverlapcheck=true

[gateaentrance]
descr=The room with the elevator to Gate A.
mesh path=GFX\map\gateaentrance_opt.rmesh
shape = 1
commonness = 0
zone1=3

[gatea]
descr=The surface of Gate A. Placed automatically in every map.
mesh path=GFX\map\gatea_opt.rmesh
shape = 1
commonness = 0
disableoverlapcheck=true

[medibay]
descr=A two-door hallway, leading to the medical bay. Contains a zombie.
mesh path=GFX\map\medibay_opt.rmesh
shape = 2
commonness = 0
zone1=3

[room2z3]
descr=An office-styled two-door hallway.
mesh path=GFX\map\room2z3_opt.rmesh
shape = 2
commonness = 75
zone1=3

[room2cafeteria]
descr=A two-doored walkway, with stairs leading to the cafeteria.
mesh path=GFX\map\room2cafeteria_opt.rmesh
shape = 2
commonness = 0
zone1=3
large = true
disabledecals = true

[room2Cz3]
descr=A corner room, with two benches against the walls.
mesh path=GFX\map\room2Cz3_opt.rmesh
shape = 2C
commonness = 100
zone1=3

[room2ccont]
descr=A corner room, which has the electrical center. Required to finish the game.
mesh path=GFX\map\room2ccont_opt.rmesh
shape = 2C
commonness = 0
zone1=3
large = true

[room2offices]
descr=A small, plain office room with various deks.
mesh path=GFX\map\room2offices_opt.rmesh
shape = 2
commonness = 30
zone1=3

[room2offices2]
descr=An office room with stairs that extends downwards.
mesh path=GFX\map\room2offices2_opt.rmesh
shape = 2
disabledecals=true
commonness = 20
zone1=3

[room2offices3]
descr=A large office with a second floor, and two seperate rooms.
mesh path=GFX\map\room2offices3_opt.rmesh
shape = 2
commonness = 20
zone1=3

[room2offices4]
descr=A varient of the offices.
mesh path=GFX\map\room2offices4_opt.rmesh
shape=2
commonness = 0
zone1=3

[room2poffices]
descr=A two-door hallway with three labeled offices (Harp, Maynard, & Gears).
mesh path=GFX\map\room2poffices_opt.rmesh
shape = 2
commonness = 0
zone1=3

[room2poffices2]
descr=A smaller hallway, with two doors leading to Dr. L's office and the conference rooms.
mesh path=GFX\map\room2poffices2_opt.rmesh
shape = 2
commonness = 0
zone1=3

[room2sroom]
descr=A two-door hallway with an office containing SCP-420-J
mesh path=GFX\map\room2sroom_opt.rmesh
shape = 2
commonness = 0
zone1=3

[room2toilets]
descr=Hallway containing SCP-789-J.
mesh path=GFX\map\room2toilets_opt.rmesh
shape = 2
commonness = 30
zone1=3

[room2tesla]
descr=Hallway with a tesla gate. EZ variant.
mesh path=GFX\map\room2tesla_opt.rmesh
shape = 2
commonness = 100
zone1=3

[room3servers]
descr=A maze of re-arranced servers, where SCP-173 spawns.
mesh path=GFX\map\room3servers_opt.rmesh
shape = 3
commonness = 0
disabledecals = true
zone1=3

[room3servers2]
descr=Another variant of the server room, with a longer catwalk.
mesh path=GFX\map\room3servers2_opt.rmesh
shape = 3
commonness = 0
disabledecals = true
zone1=3

[room3z3]
descr=A three-door hallway.
mesh path=GFX\map\room3z3_opt.rmesh
shape = 3
commonness = 100
zone1=3

[room4z3]
descr=A 4-door hallway.
mesh path=GFX\map\room4z3_opt.rmesh
shape = 4
commonness = 100
zone1=3

[room1lifts]
descr=A dead-end room with two elevators.
mesh path=GFX\map\room1lifts_opt.rmesh
shape=1
commonness = 0
zone1=3

[room3gw]
descr=An alternate, EZ-styled, broken contamination airlock.
mesh path=GFX\map\room3gw_opt.rmesh
shape=3
commonness = 10
zone1=3

[room2servers2]
descr=A server room dedicated to keep the Site-COMmunications up and running (no real value).
mesh path=GFX\map\room2servers2_opt.rmesh
shape=2
commonness = 0
zone1=3

[room3offices]
descr=A office room with three exits.
mesh path=GFX\map\room3offices_opt.rmesh
shape=3
commonness = 0
zone1=3

[room2z3_2]
descr=A varient of the two-door hallway.
mesh path=GFX\map\room2z3_2_opt.rmesh
shape=2
commonness = 25
zone1=3

[pocketdimension]
descr=SCP-106's pocket dimension. Placed automatically in all maps.
mesh path=GFX\map\pocketdimension1_opt.rmesh
shape = 1
commonness = 0

[dimension1499]
descr=SCP-1499's dimension. Placed automatically in all maps.
mesh path=GFX\map\dimension1499\1499object0_opt.rmesh
shape=1
commonness = 0
disabledecals = true
