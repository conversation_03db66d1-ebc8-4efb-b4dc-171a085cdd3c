xof 0303txt 0032
template XSkinMeshHeader {
 <3cf169ce-ff7c-44ab-93c0-f78f62d172e2>
 WORD nMaxSkinWeightsPerVertex;
 WORD nMaxSkinWeightsPerFace;
 WORD nBones;
}

template VertexDuplicationIndices {
<b8d65549-d7c9-4995-89cf-53a9a8b031e3>
 DWORD nIndices;
 DWORD nOriginalVertices;
 array DWORD indices[nIndices];
}

template SkinWeights {
 <6f0d123b-bad2-4167-a0d0-80224f25fabb>
 STRING transformNodeName;
 DWORD nWeights;
 array DWORD vertexIndices[nWeights];
 array FLOAT weights[nWeights];
 Matrix4x4 matrixOffset;
}


Frame Scene_Root {


 FrameTransformMatrix {
  1.000000, 0.000000, 0.000000, 0.000000,
  0.000000, 1.000000, 0.000000, 0.000000,
  0.000000, 0.000000, 1.000000, 0.000000,
  0.000000, 0.000000, 0.000000, 1.000000;;
 }

  Frame 294_screen {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    36;
    0.266686;1.066408;0.110896;,
    0.269804;1.053698;0.096466;,
    0.265830;1.033945;0.116615;,
    0.262300;1.044606;0.133136;,
    0.266686;1.066408;-0.196130;,
    0.269804;1.053698;-0.181699;,
    0.259216;1.068924;-0.210331;,
    0.256717;1.056505;-0.222999;,
    0.262300;1.044606;-0.218369;,
    0.265830;1.033945;-0.201849;,
    0.221363;0.841126;-0.218369;,
    0.228741;0.849592;-0.201849;,
    0.211613;0.832311;-0.222999;,
    0.209114;0.819892;-0.210331;,
    0.216977;0.819323;-0.196130;,
    0.224767;0.829839;-0.181699;,
    0.216977;0.819323;0.110896;,
    0.224767;0.829839;0.096466;,
    0.209114;0.819892;0.125098;,
    0.211613;0.832311;0.137766;,
    0.221363;0.841126;0.133136;,
    0.228741;0.849592;0.116615;,
    0.256717;1.056505;0.137766;,
    0.259216;1.068924;0.125098;,
    0.258694;0.985892;0.034452;,
    0.258694;0.985892;-0.119685;,
    0.240743;0.896666;-0.119685;,
    0.240743;0.896666;0.034452;,
    0.260837;1.076985;-0.190480;,
    0.260837;1.076985;0.105247;,
    0.252802;1.037044;-0.231222;,
    0.252802;1.037044;0.145988;,
    0.215528;0.851772;0.145988;,
    0.215528;0.851772;-0.231222;,
    0.207493;0.811831;-0.190480;,
    0.207493;0.811831;0.105247;;
    54;
    3;0,2,1;,
    3;0,3,2;,
    3;4,1,5;,
    3;4,0,1;,
    3;4,7,6;,
    3;4,8,7;,
    3;8,5,9;,
    3;8,4,5;,
    3;10,9,11;,
    3;10,8,9;,
    3;10,13,12;,
    3;10,14,13;,
    3;14,11,15;,
    3;14,10,11;,
    3;16,15,17;,
    3;16,14,15;,
    3;16,19,18;,
    3;16,20,19;,
    3;20,17,21;,
    3;20,16,17;,
    3;3,21,2;,
    3;3,20,21;,
    3;3,23,22;,
    3;3,0,23;,
    3;1,2,24;,
    3;5,24,25;,
    3;5,1,24;,
    3;9,5,25;,
    3;11,25,26;,
    3;11,9,25;,
    3;15,11,26;,
    3;17,26,27;,
    3;17,15,26;,
    3;21,17,27;,
    3;2,27,24;,
    3;2,21,27;,
    3;26,24,27;,
    3;26,25,24;,
    3;28,0,4;,
    3;28,29,0;,
    3;30,7,8;,
    3;29,23,0;,
    3;31,20,3;,
    3;31,32,20;,
    3;33,8,10;,
    3;33,30,8;,
    3;34,13,14;,
    3;32,19,20;,
    3;35,14,16;,
    3;35,34,14;,
    3;6,28,4;,
    3;22,31,3;,
    3;12,33,10;,
    3;18,35,16;;

    MeshNormals {
     36;
     0.926710;0.324004;0.190341;,
     0.998129;-0.023454;0.056464;,
     0.979575;-0.139835;0.144493;,
     0.902710;0.003334;0.430237;,
     0.926710;0.324004;-0.190341;,
     0.998129;-0.023454;-0.056464;,
     0.831418;0.420833;-0.362828;,
     0.828382;0.182624;-0.529558;,
     0.902710;0.003334;-0.430237;,
     0.979575;-0.139835;-0.144493;,
     0.831188;-0.352167;-0.430237;,
     0.957440;-0.249863;-0.144493;,
     0.693309;-0.488765;-0.529558;,
     0.603989;-0.709614;-0.362828;,
     0.729313;-0.657170;-0.190341;,
     0.929543;-0.364364;-0.056464;,
     0.729313;-0.657170;0.190341;,
     0.929543;-0.364364;0.056464;,
     0.603989;-0.709614;0.362828;,
     0.693309;-0.488765;0.529558;,
     0.831188;-0.352167;0.430237;,
     0.957440;-0.249863;0.144493;,
     0.828382;0.182624;0.529558;,
     0.831418;0.420833;0.362828;,
     0.982907;-0.183693;0.012295;,
     0.982907;-0.183693;-0.012295;,
     0.977472;-0.210705;-0.012295;,
     0.977472;-0.210705;0.012295;,
     0.828849;0.538816;-0.150619;,
     0.828849;0.538816;0.150619;,
     0.811697;-0.027974;-0.583409;,
     0.811697;-0.027974;0.583409;,
     0.759364;-0.288098;0.583409;,
     0.759364;-0.288098;-0.583409;,
     0.555995;-0.817425;-0.150619;,
     0.555995;-0.817425;0.150619;;
     54;
     3;0,2,1;,
     3;0,3,2;,
     3;4,1,5;,
     3;4,0,1;,
     3;4,7,6;,
     3;4,8,7;,
     3;8,5,9;,
     3;8,4,5;,
     3;10,9,11;,
     3;10,8,9;,
     3;10,13,12;,
     3;10,14,13;,
     3;14,11,15;,
     3;14,10,11;,
     3;16,15,17;,
     3;16,14,15;,
     3;16,19,18;,
     3;16,20,19;,
     3;20,17,21;,
     3;20,16,17;,
     3;3,21,2;,
     3;3,20,21;,
     3;3,23,22;,
     3;3,0,23;,
     3;1,2,24;,
     3;5,24,25;,
     3;5,1,24;,
     3;9,5,25;,
     3;11,25,26;,
     3;11,9,25;,
     3;15,11,26;,
     3;17,26,27;,
     3;17,15,26;,
     3;21,17,27;,
     3;2,27,24;,
     3;2,21,27;,
     3;26,24,27;,
     3;26,25,24;,
     3;28,0,4;,
     3;28,29,0;,
     3;30,7,8;,
     3;29,23,0;,
     3;31,20,3;,
     3;31,32,20;,
     3;33,8,10;,
     3;33,30,8;,
     3;34,13,14;,
     3;32,19,20;,
     3;35,14,16;,
     3;35,34,14;,
     3;6,28,4;,
     3;22,31,3;,
     3;12,33,10;,
     3;18,35,16;;
    }

    MeshTextureCoords {
     36;
     0.911055;0.754633;,
     0.898681;0.764791;,
     0.915959;0.782069;,
     0.930125;0.773704;,
     0.647783;0.754633;,
     0.660156;0.764791;,
     0.635605;0.753782;,
     0.624742;0.764645;,
     0.628712;0.773704;,
     0.642879;0.782069;,
     0.628712;0.951682;,
     0.642879;0.943317;,
     0.624742;0.960741;,
     0.635605;0.971604;,
     0.647783;0.970753;,
     0.660156;0.960595;,
     0.911055;0.970753;,
     0.898681;0.960595;,
     0.923233;0.971604;,
     0.934095;0.960741;,
     0.930125;0.951682;,
     0.915959;0.943317;,
     0.934095;0.764645;,
     0.923233;0.753782;,
     0.845504;0.823671;,
     0.713333;0.823671;,
     0.713333;0.901715;,
     0.845504;0.901715;,
     0.652627;0.746731;,
     0.906210;0.746731;,
     0.617692;0.781667;,
     0.941146;0.781667;,
     0.941146;0.943719;,
     0.617692;0.943719;,
     0.652627;0.978655;,
     0.906210;0.978655;;
    }

    VertexDuplicationIndices {
     36;
     36;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31,
     32,
     33,
     34,
     35;
    }

    MeshMaterialList {
     1;
     54;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294_screen {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      1.000000;1.000000;1.000000;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    655;
    0.274837;1.124066;-0.310312;,
    0.267837;1.124066;-0.303083;,
    -0.267837;1.124066;-0.303083;,
    -0.274837;1.124066;-0.310312;,
    -0.267837;1.124066;0.303312;,
    -0.274837;1.124066;0.310312;,
    0.260837;1.076985;-0.231222;,
    0.207493;0.811831;-0.231222;,
    0.207493;0.811831;0.145988;,
    0.260837;1.076985;0.145988;,
    0.260837;0.286905;0.152988;,
    0.267837;0.286905;0.152988;,
    0.267837;0.286905;0.297312;,
    0.260837;0.286905;0.297312;,
    0.260837;0.286905;-0.297312;,
    0.267837;0.286905;-0.297312;,
    0.267837;0.286905;-0.238222;,
    0.260837;0.286905;-0.238222;,
    0.260837;0.274701;0.297312;,
    0.267837;0.274701;0.297312;,
    0.267837;0.274701;-0.297312;,
    0.260837;0.274701;-0.297312;,
    0.267837;0.286905;-0.231222;,
    0.267837;0.286905;0.145988;,
    0.244455;0.286905;0.145988;,
    0.260837;0.286905;0.020252;,
    0.260837;0.286905;-0.093289;,
    0.260837;0.286905;-0.105485;,
    0.246650;0.286905;-0.214373;,
    0.244455;0.286905;-0.231222;,
    0.260806;0.291418;0.267974;,
    0.260806;0.299036;0.275592;,
    0.267806;0.299036;0.275592;,
    0.267806;0.291418;0.267974;,
    0.267806;0.291418;0.220065;,
    0.260806;0.291418;0.220065;,
    0.260806;0.340798;0.275592;,
    0.267806;0.340798;0.275592;,
    0.260806;0.348416;0.220065;,
    0.260806;0.340798;0.212447;,
    0.267806;0.340798;0.212447;,
    0.267806;0.348416;0.220065;,
    0.267806;0.348416;0.267974;,
    0.260806;0.348416;0.267974;,
    0.260806;0.299036;0.212447;,
    0.260806;0.291418;0.220065;,
    0.267806;0.291418;0.220065;,
    0.267806;0.299036;0.212447;,
    0.267806;0.348416;0.267974;,
    0.260806;0.348416;0.267974;,
    0.267837;1.083985;0.297312;,
    0.267837;1.117066;0.297312;,
    0.267837;1.117066;-0.297312;,
    0.267837;1.083985;-0.297312;,
    0.274837;1.124066;0.310312;,
    0.267837;1.124066;0.303312;,
    0.267837;1.124066;-0.303083;,
    0.274837;1.124066;-0.310312;,
    -0.274837;1.124066;0.310312;,
    -0.267837;1.124066;0.303312;,
    0.267837;1.124066;-0.303083;,
    0.267837;1.124066;0.303312;,
    -0.267837;1.124066;0.303312;,
    -0.267837;1.124066;-0.303083;,
    0.248377;0.532600;-0.218228;,
    0.255243;0.511510;-0.213843;,
    0.269000;0.511510;-0.094945;,
    0.263297;0.532600;-0.089271;,
    0.248377;0.532600;-0.218228;,
    0.067020;0.532600;-0.219369;,
    0.067020;0.382859;-0.219369;,
    0.248377;0.382142;-0.218228;,
    0.263297;0.382142;-0.089271;,
    0.269000;0.394276;-0.094945;,
    0.255243;0.394276;-0.213843;,
    0.248377;0.382142;-0.218228;,
    0.263297;0.382142;-0.089271;,
    0.081941;0.382859;-0.090412;,
    0.081941;0.532600;-0.090412;,
    0.263297;0.532600;-0.089271;,
    0.255728;0.398496;-0.209651;,
    0.255728;0.507290;-0.209651;,
    0.268515;0.507290;-0.099137;,
    0.268515;0.398496;-0.099137;,
    0.168076;0.398496;-0.209651;,
    0.168076;0.398496;-0.099137;,
    0.168076;0.507290;-0.099137;,
    0.168076;0.507290;-0.209651;,
    0.227399;0.074403;-0.250945;,
    0.223253;0.000000;-0.250945;,
    0.219798;0.000000;-0.242602;,
    0.222729;0.074403;-0.239671;,
    0.222729;0.074403;-0.262219;,
    0.219798;0.000000;-0.259288;,
    0.211455;0.074403;-0.266889;,
    0.211455;0.000000;-0.262743;,
    0.200181;0.074403;-0.262219;,
    0.203112;0.000000;-0.259288;,
    0.195512;0.074403;-0.250945;,
    0.199657;0.000000;-0.250945;,
    0.200181;0.074403;-0.239671;,
    0.203112;0.000000;-0.242602;,
    0.211455;0.074403;-0.235001;,
    0.211455;0.000000;-0.239147;,
    0.211455;0.000000;-0.239147;,
    0.211455;0.074403;-0.235001;,
    -0.209513;0.074403;-0.250945;,
    -0.213658;0.000000;-0.250945;,
    -0.217114;0.000000;-0.242602;,
    -0.214182;0.074403;-0.239671;,
    -0.214182;0.074403;-0.262219;,
    -0.217114;0.000000;-0.259288;,
    -0.225456;0.074403;-0.266889;,
    -0.225456;0.000000;-0.262743;,
    -0.236730;0.074403;-0.262219;,
    -0.233799;0.000000;-0.259288;,
    -0.241400;0.074403;-0.250945;,
    -0.237255;0.000000;-0.250945;,
    -0.236730;0.074403;-0.239671;,
    -0.233799;0.000000;-0.242602;,
    -0.225456;0.074403;-0.235001;,
    -0.225456;0.000000;-0.239147;,
    -0.225456;0.000000;-0.239147;,
    -0.225456;0.074403;-0.235001;,
    -0.209512;0.074403;0.250945;,
    -0.213658;0.000000;0.250945;,
    -0.217113;0.000000;0.259288;,
    -0.214182;0.074403;0.262219;,
    -0.214182;0.074403;0.239671;,
    -0.217113;0.000000;0.242602;,
    -0.225456;0.074403;0.235001;,
    -0.225456;0.000000;0.239147;,
    -0.236730;0.074403;0.239671;,
    -0.233798;0.000000;0.242602;,
    -0.241399;0.074403;0.250945;,
    -0.237254;0.000000;0.250945;,
    -0.236730;0.074403;0.262219;,
    -0.233798;0.000000;0.259288;,
    -0.225456;0.074403;0.266889;,
    -0.225456;0.000000;0.262743;,
    -0.225456;0.000000;0.262743;,
    -0.225456;0.074403;0.266889;,
    0.227252;0.074403;0.250945;,
    0.223107;0.000000;0.250945;,
    0.219651;0.000000;0.259288;,
    0.222582;0.074403;0.262219;,
    0.222582;0.074403;0.239671;,
    0.219651;0.000000;0.242602;,
    0.211308;0.074403;0.235001;,
    0.211308;0.000000;0.239147;,
    0.200035;0.074403;0.239671;,
    0.202966;0.000000;0.242602;,
    0.195365;0.074403;0.250945;,
    0.199510;0.000000;0.250945;,
    0.200035;0.074403;0.262219;,
    0.202966;0.000000;0.259288;,
    0.211308;0.074403;0.266889;,
    0.211308;0.000000;0.262743;,
    0.211308;0.000000;0.262743;,
    0.211308;0.074403;0.266889;,
    0.268090;0.847031;0.255957;,
    0.272380;0.847031;0.255957;,
    0.272380;0.847031;0.267794;,
    0.268090;0.847031;0.267794;,
    0.272380;0.820213;0.267794;,
    0.272380;0.820213;0.255957;,
    0.268090;0.820213;0.255957;,
    0.268090;0.820213;0.267794;,
    0.272380;0.844017;0.258906;,
    0.267860;0.843173;0.259749;,
    0.267860;0.843173;0.264002;,
    0.272380;0.844017;0.264845;,
    0.272380;0.823228;0.264845;,
    0.267860;0.824071;0.264002;,
    0.267860;0.824071;0.259749;,
    0.272380;0.823228;0.258906;,
    0.235337;0.843173;0.259749;,
    0.235337;0.843173;0.264002;,
    0.235337;0.824071;0.264002;,
    0.235337;0.824071;0.259749;,
    0.263173;0.678122;0.146743;,
    0.263224;0.683493;0.146743;,
    0.263224;0.683493;-0.231977;,
    0.263173;0.678122;-0.231977;,
    0.211518;0.810898;-0.231977;,
    0.215184;0.806974;-0.231977;,
    0.215184;0.806974;0.146743;,
    0.211518;0.810898;0.146743;,
    0.259557;0.687417;-0.208370;,
    0.255715;0.690655;-0.203960;,
    0.214490;0.796620;-0.203960;,
    0.215134;0.801603;-0.208370;,
    0.259557;0.687417;-0.208370;,
    0.259557;0.687417;0.123137;,
    0.259557;0.687417;0.123137;,
    0.259557;0.687417;0.146743;,
    0.215134;0.801603;0.146743;,
    0.215134;0.801603;0.123137;,
    0.259557;0.687417;0.123137;,
    0.255715;0.690655;0.118726;,
    0.259557;0.687417;-0.208370;,
    0.259557;0.687417;0.146743;,
    0.215134;0.801603;0.123137;,
    0.214490;0.796620;0.118726;,
    0.259557;0.687417;0.123137;,
    0.215134;0.801603;0.123137;,
    0.215134;0.801603;-0.208370;,
    0.215134;0.801603;-0.208370;,
    0.215134;0.801603;-0.231977;,
    0.259557;0.687417;-0.231977;,
    0.259557;0.687417;-0.208370;,
    0.259557;0.687417;-0.231977;,
    0.215134;0.801603;-0.231977;,
    0.215134;0.801603;0.146743;,
    0.274837;1.083985;0.297312;,
    0.267837;1.083985;0.297312;,
    0.267837;1.083985;-0.297312;,
    0.274837;1.083985;-0.297312;,
    0.274837;1.117066;-0.297312;,
    0.267837;1.117066;-0.297312;,
    0.267837;1.117066;0.297312;,
    0.274837;1.117066;0.297312;,
    0.274837;1.076985;-0.310312;,
    0.274837;1.124066;-0.310312;,
    0.267837;1.117066;-0.310312;,
    0.267837;0.067076;-0.310312;,
    0.274837;0.060076;-0.310312;,
    -0.274837;1.124066;-0.310312;,
    -0.267837;1.117066;-0.310312;,
    0.267837;1.117066;-0.310312;,
    0.260837;0.069057;-0.297312;,
    0.260837;0.069057;0.297312;,
    -0.274837;0.060076;-0.310312;,
    -0.267837;0.067076;-0.310312;,
    -0.267837;1.117066;-0.310312;,
    -0.274837;1.124066;-0.310312;,
    0.244455;0.286905;-0.231222;,
    0.244455;0.674714;-0.231222;,
    0.207493;0.811831;-0.231222;,
    0.260837;1.076985;-0.231222;,
    0.267837;1.076985;-0.231222;,
    0.267837;0.286905;-0.231222;,
    0.260837;0.286905;0.152988;,
    0.260837;0.996356;0.152988;,
    0.260837;1.076985;0.152988;,
    0.267837;1.076985;0.152988;,
    0.267837;0.286905;0.152988;,
    0.267837;0.277277;0.297312;,
    0.267837;0.277277;-0.297312;,
    0.262901;0.280803;0.297312;,
    0.262901;0.280803;-0.297312;,
    0.267806;0.301963;0.265047;,
    0.267806;0.299036;0.275592;,
    0.267806;0.340798;0.275592;,
    0.267806;0.337871;0.265047;,
    0.267806;0.301963;0.265047;,
    0.247686;0.280597;0.265047;,
    0.247686;0.280597;0.222992;,
    0.267806;0.301963;0.222992;,
    0.267806;0.348416;0.267974;,
    0.267806;0.348416;0.220065;,
    0.267806;0.337871;0.222992;,
    0.267806;0.337871;0.265047;,
    0.220521;0.316505;0.265047;,
    0.220521;0.307763;0.265047;,
    0.225103;0.296698;0.265047;,
    0.236622;0.285180;0.265047;,
    0.247686;0.280597;0.265047;,
    0.267806;0.301963;0.265047;,
    0.267806;0.340798;0.212447;,
    0.267806;0.299036;0.212447;,
    0.267806;0.301963;0.222992;,
    0.267806;0.337871;0.222992;,
    0.220521;0.316505;0.222992;,
    0.220521;0.316505;0.265047;,
    0.267806;0.337871;0.265047;,
    0.267806;0.291418;0.220065;,
    0.267806;0.291418;0.267974;,
    0.267806;0.301963;0.222992;,
    0.247686;0.280597;0.222992;,
    0.236622;0.285180;0.222992;,
    0.225103;0.296698;0.222992;,
    0.220521;0.307763;0.222992;,
    0.220521;0.316505;0.222992;,
    0.267806;0.337871;0.222992;,
    0.220521;0.316505;0.265047;,
    0.220521;0.316505;0.222992;,
    0.220521;0.307763;0.222992;,
    0.220521;0.307763;0.265047;,
    0.225103;0.296698;0.265047;,
    0.225103;0.296698;0.222992;,
    0.236622;0.285180;0.222992;,
    0.236622;0.285180;0.265047;,
    0.207493;0.811831;0.145988;,
    0.244455;0.674714;0.145988;,
    0.244455;0.286905;0.145988;,
    0.267837;0.286905;0.145988;,
    0.267837;1.076985;0.145988;,
    0.260837;1.076985;0.145988;,
    0.244455;0.674714;0.145988;,
    0.244455;0.674714;-0.231222;,
    0.246650;0.674714;-0.214373;,
    0.260837;0.674714;-0.105485;,
    0.260837;0.674714;-0.093289;,
    0.260837;0.674714;0.020252;,
    0.260837;0.996356;-0.238222;,
    0.260837;1.076985;-0.238222;,
    0.256937;1.021056;-0.238222;,
    0.205637;1.072356;-0.238222;,
    0.180837;1.076985;-0.238222;,
    0.227937;1.061056;-0.238222;,
    0.245567;1.043356;-0.238222;,
    0.205637;1.072356;0.152988;,
    0.180837;1.076985;0.152988;,
    0.256937;1.021056;0.152988;,
    0.245567;1.043356;0.152988;,
    0.227937;1.061056;0.152988;,
    -0.267837;0.067076;-0.310312;,
    -0.274837;0.060076;-0.310312;,
    0.274837;0.060076;-0.310312;,
    0.267837;0.067076;-0.310312;,
    -0.267837;0.067076;-0.310312;,
    0.267837;0.067076;-0.310312;,
    0.267837;1.117066;-0.310312;,
    -0.267837;1.117066;-0.310312;,
    0.260837;0.286905;-0.238222;,
    0.267837;0.286905;-0.238222;,
    0.267837;1.076985;-0.238222;,
    0.255728;0.398496;-0.209651;,
    0.168076;0.398496;-0.209651;,
    0.168076;0.507290;-0.209651;,
    0.255728;0.507290;-0.209651;,
    0.268515;0.507290;-0.099137;,
    0.168076;0.507290;-0.099137;,
    0.168076;0.398496;-0.099137;,
    0.268515;0.398496;-0.099137;,
    0.067020;0.532600;-0.219369;,
    0.081941;0.532600;-0.090412;,
    0.081941;0.382859;-0.090412;,
    0.067020;0.382859;-0.219369;,
    0.203725;0.749056;0.170868;,
    0.268090;0.749056;0.170868;,
    0.268090;0.887748;0.170868;,
    0.203725;0.887748;0.170868;,
    0.268090;0.892410;0.175531;,
    0.203725;0.892410;0.175531;,
    0.268090;0.892410;0.277191;,
    0.203725;0.892410;0.277191;,
    0.203725;0.744393;0.175531;,
    0.268090;0.744393;0.175531;,
    0.203725;0.744393;0.278139;,
    0.203725;0.749056;0.282801;,
    0.268090;0.749056;0.281853;,
    0.268090;0.744393;0.277191;,
    0.203725;0.887748;0.281853;,
    0.268090;0.887748;0.281853;,
    0.268090;0.820213;0.255957;,
    0.268090;0.847031;0.255957;,
    0.268090;0.847031;0.267794;,
    0.268090;0.820213;0.267794;,
    0.272380;0.823228;0.258906;,
    0.267860;0.824071;0.259749;,
    0.267860;0.843173;0.259749;,
    0.272380;0.844017;0.258906;,
    0.272380;0.844017;0.264845;,
    0.267860;0.843173;0.264002;,
    0.267860;0.824071;0.264002;,
    0.272380;0.823228;0.264845;,
    0.235337;0.843173;0.264002;,
    0.235337;0.824071;0.264002;,
    0.235337;0.824071;0.259749;,
    0.235337;0.843173;0.259749;,
    0.263173;0.678122;-0.231977;,
    0.239523;0.674276;-0.231977;,
    0.239523;0.674276;0.146743;,
    0.263173;0.678122;0.146743;,
    0.274837;1.083985;-0.297312;,
    0.267837;1.083985;-0.297312;,
    0.267837;1.117066;-0.297312;,
    0.274837;1.117066;-0.297312;,
    0.274837;1.117066;0.297312;,
    0.267837;1.117066;0.297312;,
    0.267837;1.083985;0.297312;,
    0.274837;1.083985;0.297312;,
    0.274837;0.060076;0.310312;,
    0.267837;0.067076;0.310312;,
    0.267837;1.117066;0.310312;,
    0.274837;1.124066;0.310312;,
    0.274837;1.076985;0.310312;,
    0.180837;1.076985;-0.238222;,
    0.260837;1.076985;-0.238222;,
    0.260837;1.076985;-0.297312;,
    0.180837;1.076985;-0.297312;,
    0.260837;1.076985;0.145988;,
    0.267837;1.076985;0.145988;,
    0.274837;1.076985;0.145988;,
    0.274837;1.076985;-0.231222;,
    0.267837;1.076985;-0.231222;,
    0.260837;1.076985;-0.231222;,
    0.244455;0.286905;-0.231222;,
    0.246650;0.388964;-0.214373;,
    0.246650;0.524061;-0.214373;,
    0.244455;0.674714;-0.231222;,
    0.274837;1.076985;0.152988;,
    0.267837;1.076985;0.152988;,
    0.260837;1.076985;0.152988;,
    0.260837;1.076985;0.297312;,
    0.274837;1.076985;0.297312;,
    0.274837;1.076985;-0.238222;,
    0.267837;1.076985;-0.238222;,
    0.246650;0.674714;-0.214373;,
    0.244455;0.674714;0.145988;,
    0.260837;0.674714;0.020252;,
    0.260837;0.286905;0.020252;,
    0.244455;0.286905;0.145988;,
    0.267837;1.117066;0.310312;,
    -0.267837;1.117066;0.310312;,
    -0.274837;1.124066;0.310312;,
    -0.267837;0.067076;0.310312;,
    -0.274837;0.060076;0.310312;,
    0.260837;1.076985;0.297312;,
    0.260837;1.076985;0.152988;,
    0.180837;1.076985;0.152988;,
    0.180837;1.076985;0.297312;,
    0.260837;1.076985;-0.297312;,
    0.260837;1.076985;-0.238222;,
    0.274837;1.076985;-0.297312;,
    -0.267837;1.117066;0.310312;,
    0.267837;0.067076;0.310312;,
    0.274837;0.060076;0.310312;,
    -0.274837;0.060076;0.310312;,
    -0.267837;0.067076;0.310312;,
    0.267837;0.067076;0.310312;,
    -0.267837;0.067076;0.310312;,
    -0.267837;1.117066;0.310312;,
    0.267837;1.117066;0.310312;,
    0.260837;0.524061;-0.093289;,
    0.260837;0.388964;-0.093289;,
    0.260837;0.674714;-0.105485;,
    0.260837;0.524061;-0.105485;,
    0.260837;0.674714;-0.093289;,
    0.260837;0.286905;-0.093289;,
    0.246650;0.286905;-0.214373;,
    0.260837;0.388964;-0.105485;,
    0.260837;0.286905;-0.105485;,
    0.248377;0.382142;-0.218228;,
    0.067020;0.382859;-0.219369;,
    0.081941;0.382859;-0.090412;,
    0.263297;0.382142;-0.089271;,
    0.263297;0.532600;-0.089271;,
    0.081941;0.532600;-0.090412;,
    0.067020;0.532600;-0.219369;,
    0.248377;0.532600;-0.218228;,
    0.255728;0.507290;-0.209651;,
    0.241119;0.507290;-0.184207;,
    0.241119;0.507290;-0.124581;,
    0.268515;0.507290;-0.099137;,
    0.182449;0.507290;-0.124581;,
    0.168076;0.507290;-0.099137;,
    0.168076;0.507290;-0.209651;,
    0.182449;0.507290;-0.184207;,
    0.268090;0.887748;0.170868;,
    0.268090;0.847031;0.255957;,
    0.268090;0.892410;0.175531;,
    0.268090;0.847031;0.267794;,
    0.268090;0.892410;0.277191;,
    0.268090;0.749056;0.170868;,
    0.268090;0.820213;0.255957;,
    0.268090;0.744393;0.175531;,
    0.268090;0.887748;0.281853;,
    0.268090;0.820213;0.267794;,
    0.268090;0.749056;0.281853;,
    0.268090;0.744393;0.277191;,
    0.272380;0.844017;0.264845;,
    0.272380;0.823228;0.264845;,
    0.272380;0.823228;0.258906;,
    0.272380;0.844017;0.258906;,
    0.274837;1.076985;0.297312;,
    0.260837;1.076985;0.297312;,
    0.260837;0.996356;0.297312;,
    0.260837;0.286905;0.297312;,
    0.267837;0.286905;0.297312;,
    0.274837;0.286905;0.297312;,
    0.180837;1.076985;-0.297312;,
    0.205637;1.072356;-0.297312;,
    0.205637;1.072356;-0.238222;,
    0.180837;1.076985;-0.238222;,
    0.274837;0.060076;0.297312;,
    0.274837;0.274701;0.297312;,
    0.267837;0.274701;0.297312;,
    0.260837;0.274701;0.297312;,
    0.260837;0.069057;0.297312;,
    -0.274837;0.060076;0.297312;,
    0.274837;0.060076;0.297312;,
    0.260837;0.069057;0.297312;,
    -0.274837;0.069057;0.297312;,
    0.260837;0.286905;-0.297312;,
    0.260837;0.286905;-0.238222;,
    0.260837;0.996356;-0.238222;,
    0.260837;0.996356;-0.297312;,
    0.267837;0.284329;0.297312;,
    0.262901;0.280803;0.297312;,
    0.267837;0.277277;0.297312;,
    0.256937;1.021056;-0.238222;,
    0.256937;1.021056;-0.297312;,
    0.260837;0.996356;0.297312;,
    0.260837;1.076985;0.297312;,
    0.256937;1.021056;0.297312;,
    0.227937;1.061056;-0.297312;,
    0.227937;1.061056;-0.238222;,
    0.245567;1.043356;-0.297312;,
    0.245567;1.043356;-0.238222;,
    0.227937;1.061056;0.297312;,
    0.205637;1.072356;0.297312;,
    0.245567;1.043356;0.297312;,
    0.180837;1.076985;0.297312;,
    0.274837;1.083985;-0.297312;,
    0.274837;1.076985;-0.297312;,
    0.274837;1.076985;-0.238222;,
    0.274837;1.076985;-0.231222;,
    0.274837;1.076985;0.145988;,
    0.274837;1.076985;0.152988;,
    0.274837;1.076985;0.297312;,
    0.274837;1.083985;0.297312;,
    0.274837;1.083985;0.297312;,
    0.274837;1.076985;0.310312;,
    0.274837;1.124066;0.310312;,
    0.274837;1.117066;0.297312;,
    0.274837;1.117066;-0.297312;,
    0.274837;1.124066;-0.310312;,
    0.274837;1.076985;-0.310312;,
    0.274837;1.083985;-0.297312;,
    0.274837;1.117066;0.297312;,
    0.274837;1.124066;0.310312;,
    0.274837;1.124066;-0.310312;,
    0.274837;1.117066;-0.297312;,
    0.274837;1.076985;0.297312;,
    0.274837;0.286905;0.297312;,
    0.274837;0.274701;0.297312;,
    0.274837;0.060076;0.297312;,
    0.274837;0.060076;0.310312;,
    0.274837;1.076985;-0.297312;,
    0.274837;0.060076;-0.297312;,
    0.274837;0.274701;-0.297312;,
    0.274837;0.286905;-0.297312;,
    0.274837;0.060076;-0.310312;,
    0.260837;0.286905;0.297312;,
    0.260806;0.291418;0.267974;,
    0.260806;0.291418;0.220065;,
    0.260837;0.286905;0.152988;,
    0.267837;1.076985;-0.238222;,
    0.267837;0.286905;-0.238222;,
    0.267837;0.286905;-0.231222;,
    0.267837;1.076985;-0.231222;,
    0.267837;1.076985;0.145988;,
    0.267837;0.286905;0.145988;,
    0.267837;0.286905;0.152988;,
    0.267837;1.076985;0.152988;,
    0.267837;0.284329;-0.297312;,
    0.267837;0.284329;0.297312;,
    0.267837;0.286905;0.297312;,
    0.267837;0.286905;0.152988;,
    0.267837;0.286905;0.145988;,
    0.267837;0.286905;-0.231222;,
    0.267837;0.286905;-0.238222;,
    0.267837;0.286905;-0.297312;,
    0.260806;0.299036;0.275592;,
    0.260837;0.747531;0.276143;,
    0.260837;0.747531;0.174157;,
    0.260806;0.299036;0.212447;,
    0.260806;0.340798;0.212447;,
    0.260806;0.348416;0.220065;,
    0.260806;0.348416;0.267974;,
    0.260806;0.340798;0.275592;,
    0.260837;0.996356;0.152988;,
    0.260837;0.888090;0.174157;,
    0.260837;0.888090;0.276143;,
    0.260837;0.996356;0.297312;,
    0.256937;1.021056;0.297312;,
    0.256937;1.021056;0.152988;,
    0.205637;1.072356;0.297312;,
    0.180837;1.076985;0.297312;,
    0.180837;1.076985;0.152988;,
    0.205637;1.072356;0.152988;,
    0.227937;1.061056;0.297312;,
    0.227937;1.061056;0.152988;,
    0.245567;1.043356;0.297312;,
    0.245567;1.043356;0.152988;,
    -0.274837;0.069057;-0.297312;,
    0.260837;0.069057;-0.297312;,
    0.274837;0.060076;-0.297312;,
    -0.274837;0.060076;-0.297312;,
    0.274837;1.076985;-0.297312;,
    0.274837;0.286905;-0.297312;,
    0.267837;0.286905;-0.297312;,
    0.260837;0.286905;-0.297312;,
    0.260837;0.996356;-0.297312;,
    0.260837;1.076985;-0.297312;,
    0.180837;1.076985;-0.297312;,
    0.260837;1.076985;-0.297312;,
    0.205637;1.072356;-0.297312;,
    0.260837;0.274701;-0.297312;,
    0.267837;0.274701;-0.297312;,
    0.274837;0.274701;-0.297312;,
    0.274837;0.060076;-0.297312;,
    0.260837;0.069057;-0.297312;,
    0.267837;0.277277;-0.297312;,
    0.262901;0.280803;-0.297312;,
    0.267837;0.284329;-0.297312;,
    0.256937;1.021056;-0.297312;,
    0.260837;0.996356;-0.297312;,
    0.245567;1.043356;-0.297312;,
    0.227937;1.061056;-0.297312;,
    0.255243;0.394276;-0.213843;,
    0.255243;0.511510;-0.213843;,
    0.269000;0.511510;-0.094945;,
    0.269000;0.394276;-0.094945;,
    0.274837;1.124066;-0.310312;,
    0.260837;0.274701;0.297312;,
    0.260837;0.274701;-0.297312;,
    0.267837;0.274701;-0.297312;,
    0.267837;0.274701;0.297312;,
    0.248377;0.382142;-0.218228;,
    0.255243;0.394276;-0.213843;,
    0.255243;0.511510;-0.213843;,
    0.248377;0.532600;-0.218228;,
    0.263297;0.532600;-0.089271;,
    0.269000;0.511510;-0.094945;,
    0.269000;0.394276;-0.094945;,
    0.263297;0.382142;-0.089271;,
    0.255243;0.511510;-0.213843;,
    0.255728;0.507290;-0.209651;,
    0.268515;0.507290;-0.099137;,
    0.269000;0.511510;-0.094945;,
    0.269000;0.394276;-0.094945;,
    0.268515;0.398496;-0.099137;,
    0.255728;0.398496;-0.209651;,
    0.255243;0.394276;-0.213843;,
    0.272380;0.820213;0.255957;,
    0.272380;0.847031;0.255957;,
    0.272380;0.847031;0.267794;,
    0.272380;0.820213;0.267794;,
    -0.274837;1.124066;0.310312;,
    0.274837;1.124066;0.310312;,
    -0.274837;1.124066;0.310312;,
    0.268515;0.398496;-0.099137;,
    0.168076;0.398496;-0.099137;,
    0.168076;0.398496;-0.209651;,
    0.255728;0.398496;-0.209651;,
    0.272380;0.847031;0.267794;,
    0.272380;0.820213;0.267794;,
    0.272380;0.820213;0.255957;,
    0.272380;0.847031;0.255957;,
    0.262901;0.280803;-0.297312;,
    0.262901;0.280803;0.297312;;
    483;
    3;0,2,1;,
    3;0,3,2;,
    3;3,4,2;,
    3;3,5,4;,
    3;6,8,7;,
    3;6,9,8;,
    3;10,12,11;,
    3;10,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;22,24,23;,
    3;22,25,24;,
    3;22,26,25;,
    3;22,27,26;,
    3;22,28,27;,
    3;22,29,28;,
    3;30,32,31;,
    3;30,33,32;,
    3;30,34,33;,
    3;30,35,34;,
    3;36,32,37;,
    3;36,31,32;,
    3;38,40,39;,
    3;38,41,40;,
    3;38,42,41;,
    3;38,43,42;,
    3;44,46,45;,
    3;44,47,46;,
    3;44,40,47;,
    3;44,39,40;,
    3;48,36,37;,
    3;48,49,36;,
    3;50,52,51;,
    3;50,53,52;,
    3;54,56,55;,
    3;54,57,56;,
    3;55,58,54;,
    3;55,59,58;,
    3;60,62,61;,
    3;60,63,62;,
    3;64,66,65;,
    3;64,67,66;,
    3;68,70,69;,
    3;68,71,70;,
    3;72,74,73;,
    3;72,75,74;,
    3;76,78,77;,
    3;76,79,78;,
    3;613,81,80;,
    3;613,614,81;,
    3;615,83,82;,
    3;615,616,83;,
    3;84,86,85;,
    3;84,87,86;,
    3;88,90,89;,
    3;88,91,90;,
    3;92,89,93;,
    3;92,88,89;,
    3;94,93,95;,
    3;94,92,93;,
    3;96,95,97;,
    3;96,94,95;,
    3;98,97,99;,
    3;98,96,97;,
    3;100,99,101;,
    3;100,98,99;,
    3;102,101,103;,
    3;102,100,101;,
    3;91,104,90;,
    3;91,105,104;,
    3;106,108,107;,
    3;106,109,108;,
    3;110,107,111;,
    3;110,106,107;,
    3;112,111,113;,
    3;112,110,111;,
    3;114,113,115;,
    3;114,112,113;,
    3;116,115,117;,
    3;116,114,115;,
    3;118,117,119;,
    3;118,116,117;,
    3;120,119,121;,
    3;120,118,119;,
    3;109,122,108;,
    3;109,123,122;,
    3;124,126,125;,
    3;124,127,126;,
    3;128,125,129;,
    3;128,124,125;,
    3;130,129,131;,
    3;130,128,129;,
    3;132,131,133;,
    3;132,130,131;,
    3;134,133,135;,
    3;134,132,133;,
    3;136,135,137;,
    3;136,134,135;,
    3;138,137,139;,
    3;138,136,137;,
    3;127,140,126;,
    3;127,141,140;,
    3;142,144,143;,
    3;142,145,144;,
    3;146,143,147;,
    3;146,142,143;,
    3;148,147,149;,
    3;148,146,147;,
    3;150,149,151;,
    3;150,148,149;,
    3;152,151,153;,
    3;152,150,151;,
    3;154,153,155;,
    3;154,152,153;,
    3;156,155,157;,
    3;156,154,155;,
    3;145,158,144;,
    3;145,159,158;,
    3;160,162,161;,
    3;160,163,162;,
    3;164,166,165;,
    3;164,167,166;,
    3;168,170,169;,
    3;168,171,170;,
    3;172,174,173;,
    3;172,175,174;,
    3;169,177,176;,
    3;169,170,177;,
    3;173,179,178;,
    3;173,174,179;,
    3;180,182,181;,
    3;180,183,182;,
    3;184,186,185;,
    3;184,187,186;,
    3;188,190,189;,
    3;188,191,190;,
    3;192,181,182;,
    3;192,193,181;,
    3;194,196,195;,
    3;194,197,196;,
    3;198,189,199;,
    3;198,200,189;,
    3;193,201,181;,
    3;202,199,203;,
    3;202,204,199;,
    3;205,185,186;,
    3;205,206,185;,
    3;207,209,208;,
    3;207,210,209;,
    3;191,203,190;,
    3;191,202,203;,
    3;211,192,182;,
    3;212,185,206;,
    3;199,190,203;,
    3;199,189,190;,
    3;186,213,205;,
    3;214,216,215;,
    3;214,217,216;,
    3;218,220,219;,
    3;218,221,220;,
    3;222,224,223;,
    3;222,225,224;,
    3;222,226,225;,
    3;227,229,228;,
    3;227,617,229;,
    3;230,618,231;,
    3;230,619,618;,
    3;232,234,233;,
    3;232,235,234;,
    3;236,238,237;,
    3;236,239,238;,
    3;236,240,239;,
    3;236,241,240;,
    3;242,244,243;,
    3;242,245,244;,
    3;242,246,245;,
    3;247,620,248;,
    3;247,621,620;,
    3;249,248,250;,
    3;249,247,248;,
    3;251,253,252;,
    3;251,254,253;,
    3;255,257,256;,
    3;255,258,257;,
    3;254,260,259;,
    3;254,261,260;,
    3;262,264,263;,
    3;262,265,264;,
    3;262,266,265;,
    3;262,267,266;,
    3;262,268,267;,
    3;261,270,269;,
    3;261,271,270;,
    3;272,274,273;,
    3;272,275,274;,
    3;271,277,276;,
    3;271,251,277;,
    3;278,280,279;,
    3;278,281,280;,
    3;278,282,281;,
    3;278,283,282;,
    3;278,284,283;,
    3;252,277,251;,
    3;259,253,254;,
    3;269,260,261;,
    3;276,270,271;,
    3;285,287,286;,
    3;285,288,287;,
    3;289,291,290;,
    3;289,292,291;,
    3;256,291,292;,
    3;256,257,291;,
    3;287,289,290;,
    3;287,288,289;,
    3;293,295,294;,
    3;293,296,295;,
    3;293,297,296;,
    3;293,298,297;,
    3;299,301,300;,
    3;299,302,301;,
    3;299,303,302;,
    3;299,304,303;,
    3;305,307,306;,
    3;308,309,306;,
    3;310,308,306;,
    3;311,310,306;,
    3;307,311,306;,
    3;244,313,312;,
    3;314,243,244;,
    3;315,314,244;,
    3;316,315,244;,
    3;312,316,244;,
    3;317,319,318;,
    3;317,320,319;,
    3;321,323,322;,
    3;321,324,323;,
    3;306,325,305;,
    3;306,326,325;,
    3;306,327,326;,
    3;622,624,623;,
    3;622,625,624;,
    3;626,628,627;,
    3;626,629,628;,
    3;630,632,631;,
    3;630,633,632;,
    3;634,636,635;,
    3;634,637,636;,
    3;328,330,329;,
    3;328,331,330;,
    3;332,334,333;,
    3;332,335,334;,
    3;336,338,337;,
    3;336,339,338;,
    3;340,342,341;,
    3;340,343,342;,
    3;343,344,342;,
    3;343,345,344;,
    3;345,346,344;,
    3;345,347,346;,
    3;341,348,340;,
    3;341,349,348;,
    3;350,352,351;,
    3;350,353,352;,
    3;350,349,353;,
    3;350,348,349;,
    3;354,352,355;,
    3;354,351,352;,
    3;346,354,355;,
    3;346,347,354;,
    3;356,639,638;,
    3;356,357,639;,
    3;358,641,640;,
    3;358,359,641;,
    3;360,362,361;,
    3;360,363,362;,
    3;364,366,365;,
    3;364,367,366;,
    3;365,369,368;,
    3;365,366,369;,
    3;361,371,370;,
    3;361,362,371;,
    3;372,374,373;,
    3;372,375,374;,
    3;376,378,377;,
    3;376,379,378;,
    3;380,382,381;,
    3;380,383,382;,
    3;384,386,385;,
    3;384,387,386;,
    3;384,388,387;,
    3;389,391,390;,
    3;389,392,391;,
    3;393,395,394;,
    3;393,396,395;,
    3;393,397,396;,
    3;393,398,397;,
    3;399,401,400;,
    3;399,402,401;,
    3;403,405,404;,
    3;403,406,405;,
    3;403,407,406;,
    3;395,404,394;,
    3;395,403,404;,
    3;408,397,409;,
    3;408,396,397;,
    3;402,410,401;,
    3;411,413,412;,
    3;411,414,413;,
    3;642,415,643;,
    3;642,416,415;,
    3;417,419,418;,
    3;417,644,419;,
    3;420,422,421;,
    3;420,423,422;,
    3;424,409,425;,
    3;424,408,409;,
    3;424,426,408;,
    3;418,427,417;,
    3;428,430,429;,
    3;428,431,430;,
    3;432,434,433;,
    3;432,435,434;,
    3;412,437,436;,
    3;412,413,437;,
    3;438,436,439;,
    3;438,440,436;,
    3;413,441,437;,
    3;410,438,439;,
    3;442,399,400;,
    3;442,400,443;,
    3;441,443,437;,
    3;441,444,443;,
    3;440,412,436;,
    3;401,410,439;,
    3;443,444,442;,
    3;445,447,446;,
    3;445,448,447;,
    3;449,451,450;,
    3;449,452,451;,
    3;453,455,454;,
    3;453,456,455;,
    3;645,647,646;,
    3;645,648,647;,
    3;456,457,455;,
    3;456,458,457;,
    3;459,454,460;,
    3;459,453,454;,
    3;458,460,457;,
    3;458,459,460;,
    3;461,463,462;,
    3;463,464,462;,
    3;463,465,464;,
    3;466,462,467;,
    3;466,461,462;,
    3;468,466,467;,
    3;465,469,464;,
    3;469,470,464;,
    3;469,471,470;,
    3;471,472,470;,
    3;472,467,470;,
    3;472,468,467;,
    3;649,474,473;,
    3;649,650,474;,
    3;651,476,475;,
    3;651,652,476;,
    3;475,650,651;,
    3;475,474,650;,
    3;473,652,649;,
    3;473,476,652;,
    3;477,479,478;,
    3;477,480,479;,
    3;477,481,480;,
    3;477,482,481;,
    3;483,485,484;,
    3;483,486,485;,
    3;487,489,488;,
    3;487,490,489;,
    3;487,491,490;,
    3;492,494,493;,
    3;492,495,494;,
    3;496,498,497;,
    3;496,499,498;,
    3;482,500,481;,
    3;482,501,500;,
    3;482,502,501;,
    3;482,489,502;,
    3;482,488,489;,
    3;499,503,498;,
    3;499,504,503;,
    3;505,507,506;,
    3;485,508,484;,
    3;485,509,508;,
    3;509,510,508;,
    3;509,511,510;,
    3;511,504,510;,
    3;511,503,504;,
    3;512,513,506;,
    3;514,512,506;,
    3;507,514,506;,
    3;515,506,513;,
    3;516,518,517;,
    3;516,519,518;,
    3;516,520,519;,
    3;516,521,520;,
    3;516,522,521;,
    3;516,523,522;,
    3;524,526,525;,
    3;524,527,526;,
    3;528,530,529;,
    3;528,531,530;,
    3;532,534,533;,
    3;532,535,534;,
    3;525,536,524;,
    3;525,537,536;,
    3;525,538,537;,
    3;525,539,538;,
    3;525,540,539;,
    3;541,530,531;,
    3;542,544,543;,
    3;542,541,544;,
    3;542,530,541;,
    3;542,545,530;,
    3;546,548,547;,
    3;546,549,548;,
    3;550,552,551;,
    3;550,553,552;,
    3;554,556,555;,
    3;554,557,556;,
    3;558,560,559;,
    3;558,561,560;,
    3;558,562,561;,
    3;558,563,562;,
    3;558,564,563;,
    3;558,565,564;,
    3;653,559,654;,
    3;653,558,559;,
    3;566,567,546;,
    3;566,568,567;,
    3;566,549,568;,
    3;566,569,549;,
    3;566,570,569;,
    3;566,571,570;,
    3;566,572,571;,
    3;566,573,572;,
    3;547,566,546;,
    3;569,548,549;,
    3;574,576,575;,
    3;574,577,576;,
    3;577,579,578;,
    3;577,574,579;,
    3;580,582,581;,
    3;580,583,582;,
    3;584,583,580;,
    3;584,585,583;,
    3;586,585,584;,
    3;586,587,585;,
    3;578,587,586;,
    3;578,579,587;,
    3;567,577,546;,
    3;567,576,577;,
    3;575,549,574;,
    3;575,568,549;,
    3;588,590,589;,
    3;588,591,590;,
    3;592,594,593;,
    3;592,595,594;,
    3;592,596,595;,
    3;592,597,596;,
    3;598,600,599;,
    3;601,603,602;,
    3;601,604,603;,
    3;601,605,604;,
    3;602,607,606;,
    3;602,608,607;,
    3;602,594,608;,
    3;602,593,594;,
    3;602,603,593;,
    3;609,610,599;,
    3;611,609,599;,
    3;612,611,599;,
    3;600,612,599;;

    MeshNormals {
     655;
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.980357;-0.197232;-0.000000;,
     0.999476;0.032382;-0.000000;,
     0.999476;0.032382;-0.000000;,
     0.980357;-0.197232;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     -0.000000;-1.000000;-0.000000;,
     -0.000000;-1.000000;-0.000000;,
     -0.000000;-1.000000;-0.000000;,
     -0.000000;-1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-0.923880;0.382683;,
     0.000000;-0.382683;0.923880;,
     0.000000;-0.382683;0.923880;,
     0.000000;-0.923880;0.382683;,
     0.000000;-0.923880;-0.382683;,
     0.000000;-0.923880;-0.382683;,
     0.000000;0.382683;0.923880;,
     0.000000;0.382683;0.923880;,
     0.000000;0.923880;-0.382683;,
     0.000000;0.382683;-0.923880;,
     0.000000;0.382683;-0.923880;,
     0.000000;0.923880;-0.382683;,
     0.000000;0.923880;0.382683;,
     0.000000;0.923880;0.382683;,
     0.000000;-0.382683;-0.923880;,
     0.000000;-0.923880;-0.382683;,
     0.000000;-0.923880;-0.382683;,
     0.000000;-0.382683;-0.923880;,
     0.000000;0.923880;0.382683;,
     0.000000;0.923880;0.382683;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.951598;0.286945;-0.110104;,
     0.951598;0.286945;-0.110104;,
     0.951598;0.286945;-0.110104;,
     0.951598;0.286945;-0.110104;,
     0.006292;0.000000;-0.999980;,
     0.006292;0.000000;-0.999980;,
     0.006292;0.000000;-0.999980;,
     0.006292;0.000000;-0.999980;,
     0.881102;-0.461808;-0.101947;,
     0.881102;-0.461808;-0.101947;,
     0.881102;-0.461808;-0.101947;,
     0.881102;-0.461808;-0.101947;,
     -0.006292;0.000000;0.999980;,
     -0.006292;0.000000;0.999980;,
     -0.006292;0.000000;0.999980;,
     -0.006292;0.000000;0.999980;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.899337;0.437256;-0.000000;,
     0.858390;-0.512997;-0.000000;,
     0.606974;-0.512997;0.606974;,
     0.635927;0.437256;0.635927;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;0.437256;-0.899337;,
     0.000000;-0.512997;-0.858390;,
     -0.635927;0.437256;-0.635927;,
     -0.606974;-0.512997;-0.606974;,
     -0.899337;0.437256;-0.000000;,
     -0.858390;-0.512997;-0.000000;,
     -0.635927;0.437256;0.635927;,
     -0.606974;-0.512997;0.606974;,
     0.000000;0.437256;0.899337;,
     0.000000;-0.512997;0.858390;,
     0.000000;-0.512997;0.858390;,
     0.000000;0.437256;0.899337;,
     0.899337;0.437256;-0.000000;,
     0.858390;-0.512997;-0.000000;,
     0.606974;-0.512997;0.606974;,
     0.635927;0.437256;0.635927;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     -0.000000;0.437256;-0.899337;,
     -0.000000;-0.512997;-0.858390;,
     -0.635927;0.437256;-0.635927;,
     -0.606974;-0.512997;-0.606974;,
     -0.899337;0.437256;-0.000000;,
     -0.858390;-0.512997;-0.000000;,
     -0.635927;0.437256;0.635927;,
     -0.606974;-0.512997;0.606974;,
     -0.000000;0.437256;0.899337;,
     -0.000000;-0.512997;0.858390;,
     -0.000000;-0.512997;0.858390;,
     -0.000000;0.437256;0.899337;,
     0.899337;0.437256;-0.000000;,
     0.858390;-0.512997;-0.000000;,
     0.606974;-0.512997;0.606974;,
     0.635927;0.437256;0.635927;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;0.437256;-0.899337;,
     0.000000;-0.512997;-0.858390;,
     -0.635927;0.437256;-0.635927;,
     -0.606974;-0.512997;-0.606974;,
     -0.899337;0.437256;-0.000000;,
     -0.858390;-0.512997;-0.000000;,
     -0.635927;0.437256;0.635927;,
     -0.606974;-0.512997;0.606974;,
     0.000000;0.437256;0.899337;,
     0.000000;-0.512997;0.858390;,
     0.000000;-0.512997;0.858390;,
     0.000000;0.437256;0.899337;,
     0.899337;0.437256;-0.000000;,
     0.858390;-0.512997;-0.000000;,
     0.606974;-0.512997;0.606974;,
     0.635927;0.437256;0.635927;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;0.437256;-0.899337;,
     0.000000;-0.512997;-0.858390;,
     -0.635927;0.437256;-0.635927;,
     -0.606974;-0.512997;-0.606974;,
     -0.899337;0.437256;-0.000000;,
     -0.858390;-0.512997;-0.000000;,
     -0.635927;0.437256;0.635927;,
     -0.606974;-0.512997;0.606974;,
     0.000000;0.437256;0.899337;,
     0.000000;-0.512997;0.858390;,
     0.000000;-0.512997;0.858390;,
     0.000000;0.437256;0.899337;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.183456;-0.983028;-0.000000;,
     0.092120;-0.995748;-0.000000;,
     0.092120;-0.995748;-0.000000;,
     0.183456;-0.983028;-0.000000;,
     0.183456;0.983028;-0.000000;,
     0.092120;0.995748;-0.000000;,
     0.092120;0.995748;-0.000000;,
     0.183456;0.983028;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.999955;-0.009464;-0.000000;,
     0.875864;0.482559;-0.000000;,
     0.875864;0.482559;-0.000000;,
     0.999955;-0.009464;-0.000000;,
     0.730652;0.682750;-0.000000;,
     0.971698;0.236227;-0.000000;,
     0.971698;0.236227;-0.000000;,
     0.730652;0.682750;-0.000000;,
     0.804020;0.586154;0.099873;,
     0.843820;0.509392;0.168784;,
     0.966213;0.194794;0.168784;,
     0.988754;0.111317;0.099873;,
     0.804020;0.586154;0.099873;,
     0.804020;0.586154;-0.099873;,
     0.804020;0.586154;-0.099873;,
     0.846578;0.532265;-0.000000;,
     0.983703;0.179799;-0.000000;,
     0.988754;0.111317;-0.099873;,
     0.804020;0.586154;-0.099873;,
     0.843820;0.509392;-0.168784;,
     0.804020;0.586154;0.099873;,
     0.846578;0.532265;-0.000000;,
     0.988754;0.111317;-0.099873;,
     0.966213;0.194794;-0.168784;,
     0.804020;0.586154;-0.099873;,
     0.988754;0.111317;-0.099873;,
     0.988754;0.111317;0.099873;,
     0.988754;0.111317;0.099873;,
     0.983703;0.179799;-0.000000;,
     0.846578;0.532265;-0.000000;,
     0.804020;0.586154;0.099873;,
     0.846578;0.532265;-0.000000;,
     0.983703;0.179799;-0.000000;,
     0.983703;0.179799;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.889168;0.457582;-0.000000;,
     0.889168;0.457582;-0.000000;,
     0.581238;0.813733;-0.000000;,
     0.581238;0.813733;-0.000000;,
     0.882239;0.266207;-0.388314;,
     1.000000;0.000000;-0.000000;,
     1.000000;-0.000000;-0.000000;,
     0.929578;-0.248291;-0.272462;,
     0.882239;0.266207;-0.388314;,
     -0.179307;0.835627;-0.519208;,
     -0.179307;0.835627;0.519208;,
     0.882239;0.266207;0.388314;,
     1.000000;-0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.929578;-0.248291;0.272462;,
     0.929578;-0.248291;-0.272462;,
     0.721990;-0.466039;-0.511407;,
     0.873791;0.173808;-0.454182;,
     0.740764;0.494963;-0.454182;,
     0.494963;0.740764;-0.454182;,
     -0.179307;0.835627;-0.519208;,
     0.882239;0.266207;-0.388314;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.882239;0.266207;0.388314;,
     0.929578;-0.248291;0.272462;,
     0.721990;-0.466039;0.511407;,
     0.721990;-0.466039;-0.511407;,
     0.929578;-0.248291;-0.272462;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.882239;0.266207;0.388314;,
     -0.179307;0.835627;0.519208;,
     0.494963;0.740764;0.454182;,
     0.740764;0.494963;0.454182;,
     0.873791;0.173808;0.454182;,
     0.721990;-0.466039;0.511407;,
     0.929578;-0.248291;0.272462;,
     0.721990;-0.466039;-0.511407;,
     0.721990;-0.466039;0.511407;,
     0.873791;0.173808;0.454182;,
     0.873791;0.173808;-0.454182;,
     0.740764;0.494963;-0.454182;,
     0.740764;0.494963;0.454182;,
     0.494963;0.740764;0.454182;,
     0.494963;0.740764;-0.454182;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     -0.993373;0.000000;0.114937;,
     -0.993373;0.000000;0.114937;,
     -0.993373;0.000000;0.114937;,
     -0.993373;0.000000;0.114937;,
     0.000000;-0.382683;-0.923880;,
     0.000000;-0.382683;-0.923880;,
     0.000000;0.382683;-0.923880;,
     0.000000;0.382683;-0.923880;,
     0.000000;0.923880;-0.382683;,
     0.000000;0.923880;-0.382683;,
     0.000000;0.923880;0.382683;,
     0.000000;0.923880;0.382683;,
     0.000000;-0.923880;-0.382683;,
     0.000000;-0.923880;-0.382683;,
     0.005638;-0.923869;0.382667;,
     0.009632;-0.381081;0.924492;,
     0.009632;-0.381081;0.924492;,
     0.005638;-0.923869;0.382667;,
     0.003984;0.384264;0.923214;,
     0.003984;0.384264;0.923214;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.183456;0.000000;0.983028;,
     0.092120;0.000000;0.995748;,
     0.092120;0.000000;0.995748;,
     0.183456;0.000000;0.983028;,
     0.183456;0.000000;-0.983028;,
     0.092120;0.000000;-0.995748;,
     0.092120;0.000000;-0.995748;,
     0.183456;0.000000;-0.983028;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.113504;-0.697938;-0.707107;,
     -0.417356;-0.730152;-0.541011;,
     -0.417356;-0.730152;0.541011;,
     0.113504;-0.697938;0.707107;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.991619;0.000001;-0.129197;,
     0.991619;0.000001;-0.129197;,
     0.991619;0.000001;-0.129197;,
     0.991619;0.000001;-0.129197;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.991619;0.000001;-0.129198;,
     0.991619;0.000001;0.129198;,
     0.999069;0.000001;0.043146;,
     0.999069;0.000001;0.043146;,
     0.991619;0.000001;0.129198;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.316228;-0.000000;0.948683;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     1.000000;0.000001;-0.000000;,
     1.000000;0.000001;-0.000000;,
     0.997903;0.000001;-0.064735;,
     0.996270;0.000001;-0.086293;,
     1.000000;0.000001;-0.000000;,
     1.000000;0.000001;-0.000000;,
     0.991619;0.000001;-0.129198;,
     0.996270;0.000001;-0.086293;,
     0.997903;0.000001;-0.064735;,
     -0.003954;-0.999992;0.000457;,
     -0.003954;-0.999992;0.000457;,
     -0.003954;-0.999992;0.000457;,
     -0.003954;-0.999992;0.000457;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     -0.386821;-0.836729;0.387626;,
     -0.386821;-0.836729;-0.387626;,
     0.000000;-1.000000;-0.000000;,
     0.372817;-0.847483;-0.377863;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.372817;-0.847483;0.377863;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;-0.000000;-0.000000;,
     1.000000;-0.000000;-0.000000;,
     1.000000;-0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.183502;0.983019;-0.000000;,
     0.320996;0.947081;-0.000000;,
     0.320996;0.947081;-0.000000;,
     0.183502;0.983019;-0.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.996936;0.078221;-0.000000;,
     0.996936;0.078221;-0.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.951089;0.308918;-0.000000;,
     0.951089;0.308918;-0.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;-1.000000;,
     0.587687;0.809088;-0.000000;,
     0.587687;0.809088;-0.000000;,
     0.809520;0.587093;-0.000000;,
     0.809520;0.587093;-0.000000;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.999998;0.001972;-0.000234;,
     0.999992;0.003976;-0.000468;,
     0.999993;0.003729;0.000221;,
     0.999998;0.001849;0.000111;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.889168;-0.457582;-0.000000;,
     0.889168;-0.457582;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000436;-0.000468;,
     1.000000;-0.000016;-0.000000;,
     1.000000;-0.000016;-0.000000;,
     1.000000;0.000190;0.000221;,
     1.000000;-0.000063;-0.000000;,
     1.000000;-0.000063;-0.000000;,
     1.000000;-0.000063;-0.000000;,
     1.000000;-0.000063;-0.000000;,
     0.998640;0.052129;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.998640;0.052129;-0.000000;,
     0.951089;0.308918;-0.000000;,
     0.951089;0.308918;-0.000000;,
     0.320996;0.947081;-0.000000;,
     0.183502;0.983019;-0.000000;,
     0.183502;0.983019;-0.000000;,
     0.320996;0.947081;-0.000000;,
     0.587687;0.809088;-0.000000;,
     0.587687;0.809088;-0.000000;,
     0.809520;0.587093;-0.000000;,
     0.809520;0.587093;-0.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.000000;0.000000;-1.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.538226;0.000000;-0.842801;,
     0.538226;0.000000;-0.842801;,
     0.538226;0.000000;-0.842801;,
     0.538226;0.000000;-0.842801;,
     0.705272;0.000000;0.708937;,
     0.705272;0.000000;0.708937;,
     0.705272;0.000000;0.708937;,
     0.705272;0.000000;0.708937;,
     0.993373;-0.000000;-0.114937;,
     0.993373;-0.000000;-0.114937;,
     0.993373;-0.000000;-0.114937;,
     0.993373;-0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.993373;0.000000;-0.114937;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     -0.316228;-0.000000;0.948683;,
     0.000000;0.000000;1.000000;,
     -0.707107;0.000000;0.707107;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.581238;-0.813733;-0.000000;,
     0.581238;-0.813733;-0.000000;;
     483;
     3;0,2,1;,
     3;0,3,2;,
     3;3,4,2;,
     3;3,5,4;,
     3;6,8,7;,
     3;6,9,8;,
     3;10,12,11;,
     3;10,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;22,24,23;,
     3;22,25,24;,
     3;22,26,25;,
     3;22,27,26;,
     3;22,28,27;,
     3;22,29,28;,
     3;30,32,31;,
     3;30,33,32;,
     3;30,34,33;,
     3;30,35,34;,
     3;36,32,37;,
     3;36,31,32;,
     3;38,40,39;,
     3;38,41,40;,
     3;38,42,41;,
     3;38,43,42;,
     3;44,46,45;,
     3;44,47,46;,
     3;44,40,47;,
     3;44,39,40;,
     3;48,36,37;,
     3;48,49,36;,
     3;50,52,51;,
     3;50,53,52;,
     3;54,56,55;,
     3;54,57,56;,
     3;55,58,54;,
     3;55,59,58;,
     3;60,62,61;,
     3;60,63,62;,
     3;64,66,65;,
     3;64,67,66;,
     3;68,70,69;,
     3;68,71,70;,
     3;72,74,73;,
     3;72,75,74;,
     3;76,78,77;,
     3;76,79,78;,
     3;613,81,80;,
     3;613,614,81;,
     3;615,83,82;,
     3;615,616,83;,
     3;84,86,85;,
     3;84,87,86;,
     3;88,90,89;,
     3;88,91,90;,
     3;92,89,93;,
     3;92,88,89;,
     3;94,93,95;,
     3;94,92,93;,
     3;96,95,97;,
     3;96,94,95;,
     3;98,97,99;,
     3;98,96,97;,
     3;100,99,101;,
     3;100,98,99;,
     3;102,101,103;,
     3;102,100,101;,
     3;91,104,90;,
     3;91,105,104;,
     3;106,108,107;,
     3;106,109,108;,
     3;110,107,111;,
     3;110,106,107;,
     3;112,111,113;,
     3;112,110,111;,
     3;114,113,115;,
     3;114,112,113;,
     3;116,115,117;,
     3;116,114,115;,
     3;118,117,119;,
     3;118,116,117;,
     3;120,119,121;,
     3;120,118,119;,
     3;109,122,108;,
     3;109,123,122;,
     3;124,126,125;,
     3;124,127,126;,
     3;128,125,129;,
     3;128,124,125;,
     3;130,129,131;,
     3;130,128,129;,
     3;132,131,133;,
     3;132,130,131;,
     3;134,133,135;,
     3;134,132,133;,
     3;136,135,137;,
     3;136,134,135;,
     3;138,137,139;,
     3;138,136,137;,
     3;127,140,126;,
     3;127,141,140;,
     3;142,144,143;,
     3;142,145,144;,
     3;146,143,147;,
     3;146,142,143;,
     3;148,147,149;,
     3;148,146,147;,
     3;150,149,151;,
     3;150,148,149;,
     3;152,151,153;,
     3;152,150,151;,
     3;154,153,155;,
     3;154,152,153;,
     3;156,155,157;,
     3;156,154,155;,
     3;145,158,144;,
     3;145,159,158;,
     3;160,162,161;,
     3;160,163,162;,
     3;164,166,165;,
     3;164,167,166;,
     3;168,170,169;,
     3;168,171,170;,
     3;172,174,173;,
     3;172,175,174;,
     3;169,177,176;,
     3;169,170,177;,
     3;173,179,178;,
     3;173,174,179;,
     3;180,182,181;,
     3;180,183,182;,
     3;184,186,185;,
     3;184,187,186;,
     3;188,190,189;,
     3;188,191,190;,
     3;192,181,182;,
     3;192,193,181;,
     3;194,196,195;,
     3;194,197,196;,
     3;198,189,199;,
     3;198,200,189;,
     3;193,201,181;,
     3;202,199,203;,
     3;202,204,199;,
     3;205,185,186;,
     3;205,206,185;,
     3;207,209,208;,
     3;207,210,209;,
     3;191,203,190;,
     3;191,202,203;,
     3;211,192,182;,
     3;212,185,206;,
     3;199,190,203;,
     3;199,189,190;,
     3;186,213,205;,
     3;214,216,215;,
     3;214,217,216;,
     3;218,220,219;,
     3;218,221,220;,
     3;222,224,223;,
     3;222,225,224;,
     3;222,226,225;,
     3;227,229,228;,
     3;227,617,229;,
     3;230,618,231;,
     3;230,619,618;,
     3;232,234,233;,
     3;232,235,234;,
     3;236,238,237;,
     3;236,239,238;,
     3;236,240,239;,
     3;236,241,240;,
     3;242,244,243;,
     3;242,245,244;,
     3;242,246,245;,
     3;247,620,248;,
     3;247,621,620;,
     3;249,248,250;,
     3;249,247,248;,
     3;251,253,252;,
     3;251,254,253;,
     3;255,257,256;,
     3;255,258,257;,
     3;254,260,259;,
     3;254,261,260;,
     3;262,264,263;,
     3;262,265,264;,
     3;262,266,265;,
     3;262,267,266;,
     3;262,268,267;,
     3;261,270,269;,
     3;261,271,270;,
     3;272,274,273;,
     3;272,275,274;,
     3;271,277,276;,
     3;271,251,277;,
     3;278,280,279;,
     3;278,281,280;,
     3;278,282,281;,
     3;278,283,282;,
     3;278,284,283;,
     3;252,277,251;,
     3;259,253,254;,
     3;269,260,261;,
     3;276,270,271;,
     3;285,287,286;,
     3;285,288,287;,
     3;289,291,290;,
     3;289,292,291;,
     3;256,291,292;,
     3;256,257,291;,
     3;287,289,290;,
     3;287,288,289;,
     3;293,295,294;,
     3;293,296,295;,
     3;293,297,296;,
     3;293,298,297;,
     3;299,301,300;,
     3;299,302,301;,
     3;299,303,302;,
     3;299,304,303;,
     3;305,307,306;,
     3;308,309,306;,
     3;310,308,306;,
     3;311,310,306;,
     3;307,311,306;,
     3;244,313,312;,
     3;314,243,244;,
     3;315,314,244;,
     3;316,315,244;,
     3;312,316,244;,
     3;317,319,318;,
     3;317,320,319;,
     3;321,323,322;,
     3;321,324,323;,
     3;306,325,305;,
     3;306,326,325;,
     3;306,327,326;,
     3;622,624,623;,
     3;622,625,624;,
     3;626,628,627;,
     3;626,629,628;,
     3;630,632,631;,
     3;630,633,632;,
     3;634,636,635;,
     3;634,637,636;,
     3;328,330,329;,
     3;328,331,330;,
     3;332,334,333;,
     3;332,335,334;,
     3;336,338,337;,
     3;336,339,338;,
     3;340,342,341;,
     3;340,343,342;,
     3;343,344,342;,
     3;343,345,344;,
     3;345,346,344;,
     3;345,347,346;,
     3;341,348,340;,
     3;341,349,348;,
     3;350,352,351;,
     3;350,353,352;,
     3;350,349,353;,
     3;350,348,349;,
     3;354,352,355;,
     3;354,351,352;,
     3;346,354,355;,
     3;346,347,354;,
     3;356,639,638;,
     3;356,357,639;,
     3;358,641,640;,
     3;358,359,641;,
     3;360,362,361;,
     3;360,363,362;,
     3;364,366,365;,
     3;364,367,366;,
     3;365,369,368;,
     3;365,366,369;,
     3;361,371,370;,
     3;361,362,371;,
     3;372,374,373;,
     3;372,375,374;,
     3;376,378,377;,
     3;376,379,378;,
     3;380,382,381;,
     3;380,383,382;,
     3;384,386,385;,
     3;384,387,386;,
     3;384,388,387;,
     3;389,391,390;,
     3;389,392,391;,
     3;393,395,394;,
     3;393,396,395;,
     3;393,397,396;,
     3;393,398,397;,
     3;399,401,400;,
     3;399,402,401;,
     3;403,405,404;,
     3;403,406,405;,
     3;403,407,406;,
     3;395,404,394;,
     3;395,403,404;,
     3;408,397,409;,
     3;408,396,397;,
     3;402,410,401;,
     3;411,413,412;,
     3;411,414,413;,
     3;642,415,643;,
     3;642,416,415;,
     3;417,419,418;,
     3;417,644,419;,
     3;420,422,421;,
     3;420,423,422;,
     3;424,409,425;,
     3;424,408,409;,
     3;424,426,408;,
     3;418,427,417;,
     3;428,430,429;,
     3;428,431,430;,
     3;432,434,433;,
     3;432,435,434;,
     3;412,437,436;,
     3;412,413,437;,
     3;438,436,439;,
     3;438,440,436;,
     3;413,441,437;,
     3;410,438,439;,
     3;442,399,400;,
     3;442,400,443;,
     3;441,443,437;,
     3;441,444,443;,
     3;440,412,436;,
     3;401,410,439;,
     3;443,444,442;,
     3;445,447,446;,
     3;445,448,447;,
     3;449,451,450;,
     3;449,452,451;,
     3;453,455,454;,
     3;453,456,455;,
     3;645,647,646;,
     3;645,648,647;,
     3;456,457,455;,
     3;456,458,457;,
     3;459,454,460;,
     3;459,453,454;,
     3;458,460,457;,
     3;458,459,460;,
     3;461,463,462;,
     3;463,464,462;,
     3;463,465,464;,
     3;466,462,467;,
     3;466,461,462;,
     3;468,466,467;,
     3;465,469,464;,
     3;469,470,464;,
     3;469,471,470;,
     3;471,472,470;,
     3;472,467,470;,
     3;472,468,467;,
     3;649,474,473;,
     3;649,650,474;,
     3;651,476,475;,
     3;651,652,476;,
     3;475,650,651;,
     3;475,474,650;,
     3;473,652,649;,
     3;473,476,652;,
     3;477,479,478;,
     3;477,480,479;,
     3;477,481,480;,
     3;477,482,481;,
     3;483,485,484;,
     3;483,486,485;,
     3;487,489,488;,
     3;487,490,489;,
     3;487,491,490;,
     3;492,494,493;,
     3;492,495,494;,
     3;496,498,497;,
     3;496,499,498;,
     3;482,500,481;,
     3;482,501,500;,
     3;482,502,501;,
     3;482,489,502;,
     3;482,488,489;,
     3;499,503,498;,
     3;499,504,503;,
     3;505,507,506;,
     3;485,508,484;,
     3;485,509,508;,
     3;509,510,508;,
     3;509,511,510;,
     3;511,504,510;,
     3;511,503,504;,
     3;512,513,506;,
     3;514,512,506;,
     3;507,514,506;,
     3;515,506,513;,
     3;516,518,517;,
     3;516,519,518;,
     3;516,520,519;,
     3;516,521,520;,
     3;516,522,521;,
     3;516,523,522;,
     3;524,526,525;,
     3;524,527,526;,
     3;528,530,529;,
     3;528,531,530;,
     3;532,534,533;,
     3;532,535,534;,
     3;525,536,524;,
     3;525,537,536;,
     3;525,538,537;,
     3;525,539,538;,
     3;525,540,539;,
     3;541,530,531;,
     3;542,544,543;,
     3;542,541,544;,
     3;542,530,541;,
     3;542,545,530;,
     3;546,548,547;,
     3;546,549,548;,
     3;550,552,551;,
     3;550,553,552;,
     3;554,556,555;,
     3;554,557,556;,
     3;558,560,559;,
     3;558,561,560;,
     3;558,562,561;,
     3;558,563,562;,
     3;558,564,563;,
     3;558,565,564;,
     3;653,559,654;,
     3;653,558,559;,
     3;566,567,546;,
     3;566,568,567;,
     3;566,549,568;,
     3;566,569,549;,
     3;566,570,569;,
     3;566,571,570;,
     3;566,572,571;,
     3;566,573,572;,
     3;547,566,546;,
     3;569,548,549;,
     3;574,576,575;,
     3;574,577,576;,
     3;577,579,578;,
     3;577,574,579;,
     3;580,582,581;,
     3;580,583,582;,
     3;584,583,580;,
     3;584,585,583;,
     3;586,585,584;,
     3;586,587,585;,
     3;578,587,586;,
     3;578,579,587;,
     3;567,577,546;,
     3;567,576,577;,
     3;575,549,574;,
     3;575,568,549;,
     3;588,590,589;,
     3;588,591,590;,
     3;592,594,593;,
     3;592,595,594;,
     3;592,596,595;,
     3;592,597,596;,
     3;598,600,599;,
     3;601,603,602;,
     3;601,604,603;,
     3;601,605,604;,
     3;602,607,606;,
     3;602,608,607;,
     3;602,594,608;,
     3;602,593,594;,
     3;602,603,593;,
     3;609,610,599;,
     3;611,609,599;,
     3;612,611,599;,
     3;600,612,599;;
    }

    MeshTextureCoords {
     655;
     0.588549;0.006394;,
     0.582370;0.012442;,
     0.582370;0.451874;,
     0.588549;0.457913;,
     0.582370;0.012442;,
     0.588549;0.006394;,
     0.933550;0.079639;,
     0.697672;0.079639;,
     0.697672;0.408607;,
     0.933550;0.408607;,
     0.199002;0.532312;,
     0.217559;0.532312;,
     0.217559;0.404331;,
     0.199002;0.404331;,
     0.198743;0.067380;,
     0.217301;0.067380;,
     0.217301;0.003668;,
     0.198743;0.003668;,
     0.413378;0.532266;,
     0.414697;0.532266;,
     0.414697;0.004006;,
     0.413378;0.004006;,
     0.217035;0.397831;,
     0.217035;0.074171;,
     0.196688;0.074171;,
     0.210944;0.182057;,
     0.210944;0.236001;,
     0.210944;0.289944;,
     0.203816;0.343887;,
     0.196688;0.397831;,
     0.623849;0.389330;,
     0.623849;0.374942;,
     0.615235;0.374942;,
     0.615235;0.389330;,
     0.615235;0.449949;,
     0.623849;0.449949;,
     0.623849;0.316130;,
     0.615235;0.316130;,
     0.623849;0.361604;,
     0.623849;0.375992;,
     0.615235;0.375992;,
     0.615235;0.361604;,
     0.615235;0.300985;,
     0.623849;0.300985;,
     0.623849;0.434804;,
     0.623849;0.450198;,
     0.615235;0.450198;,
     0.615235;0.434804;,
     0.615235;0.300736;,
     0.623849;0.300736;,
     0.703023;0.014597;,
     0.671127;0.014597;,
     0.671127;0.587937;,
     0.703023;0.587937;,
     0.588549;0.006394;,
     0.582370;0.012442;,
     0.582370;0.451874;,
     0.588549;0.457913;,
     0.588549;0.457913;,
     0.582370;0.451874;,
     0.978549;0.514711;,
     0.978549;0.143823;,
     0.650916;0.143823;,
     0.650916;0.514711;,
     0.059747;0.330034;,
     0.064262;0.348537;,
     0.169270;0.348537;,
     0.173639;0.330034;,
     0.768506;0.049328;,
     0.644840;0.049328;,
     0.644840;0.149239;,
     0.768506;0.149718;,
     0.173639;0.462034;,
     0.169270;0.451389;,
     0.064262;0.451389;,
     0.059747;0.462034;,
     0.644840;0.149718;,
     0.768506;0.149240;,
     0.768506;0.049328;,
     0.644840;0.049329;,
     0.067964;0.447687;,
     0.067964;0.352239;,
     0.165567;0.352239;,
     0.165567;0.447687;,
     0.206001;0.780491;,
     0.306790;0.780491;,
     0.306790;0.679775;,
     0.206001;0.679775;,
     0.613881;0.673591;,
     0.611188;0.727255;,
     0.615003;0.727255;,
     0.619037;0.673591;,
     0.608707;0.673591;,
     0.607360;0.727255;,
     0.603524;0.673591;,
     0.603524;0.727255;,
     0.598340;0.673591;,
     0.599688;0.727255;,
     0.593166;0.673591;,
     0.595859;0.727255;,
     0.588011;0.673591;,
     0.592044;0.727255;,
     0.582884;0.673591;,
     0.588250;0.727255;,
     0.618797;0.727255;,
     0.624164;0.673591;,
     0.613881;0.673591;,
     0.611188;0.727255;,
     0.615003;0.727255;,
     0.619037;0.673591;,
     0.608707;0.673591;,
     0.607360;0.727255;,
     0.603524;0.673591;,
     0.603524;0.727255;,
     0.598340;0.673591;,
     0.599688;0.727255;,
     0.593166;0.673591;,
     0.595859;0.727255;,
     0.588011;0.673591;,
     0.592044;0.727255;,
     0.582884;0.673591;,
     0.588250;0.727255;,
     0.618797;0.727255;,
     0.624164;0.673591;,
     0.613881;0.673591;,
     0.611188;0.727255;,
     0.615003;0.727255;,
     0.619037;0.673591;,
     0.608707;0.673591;,
     0.607360;0.727255;,
     0.603524;0.673591;,
     0.603524;0.727255;,
     0.598340;0.673591;,
     0.599688;0.727255;,
     0.593166;0.673591;,
     0.595859;0.727255;,
     0.588011;0.673591;,
     0.592044;0.727255;,
     0.582884;0.673591;,
     0.588250;0.727255;,
     0.618797;0.727255;,
     0.624164;0.673591;,
     0.613881;0.673591;,
     0.611188;0.727255;,
     0.615003;0.727255;,
     0.619037;0.673591;,
     0.608707;0.673591;,
     0.607360;0.727255;,
     0.603524;0.673591;,
     0.603524;0.727255;,
     0.598340;0.673591;,
     0.599688;0.727255;,
     0.593166;0.673591;,
     0.595859;0.727255;,
     0.588011;0.673591;,
     0.592044;0.727255;,
     0.582884;0.673591;,
     0.588250;0.727255;,
     0.618797;0.727255;,
     0.624164;0.673591;,
     0.160989;0.842594;,
     0.160989;0.847587;,
     0.174764;0.847587;,
     0.174764;0.842594;,
     0.174764;0.878799;,
     0.160989;0.878799;,
     0.160989;0.883791;,
     0.174764;0.883791;,
     0.182096;0.832289;,
     0.181055;0.828324;,
     0.175802;0.828324;,
     0.174761;0.832289;,
     0.174762;0.832289;,
     0.175804;0.828324;,
     0.181056;0.828324;,
     0.182098;0.832289;,
     0.181053;0.800274;,
     0.175801;0.800274;,
     0.175805;0.800274;,
     0.181058;0.800274;,
     0.964318;0.718753;,
     0.964318;0.723212;,
     0.631458;0.723212;,
     0.631458;0.718753;,
     0.631458;0.718753;,
     0.631458;0.723212;,
     0.964318;0.723212;,
     0.964318;0.718753;,
     0.521670;0.996600;,
     0.515510;0.991428;,
     0.515519;0.895517;,
     0.521666;0.891124;,
     0.652967;0.727672;,
     0.943579;0.727672;,
     0.461482;0.391912;,
     0.482904;0.391912;,
     0.482904;0.284468;,
     0.461482;0.284468;,
     0.237770;0.996555;,
     0.244237;0.991440;,
     0.521643;0.996555;,
     0.964318;0.727672;,
     0.237793;0.891136;,
     0.244219;0.895528;,
     0.237797;0.996612;,
     0.943668;0.727672;,
     0.653056;0.727672;,
     0.459204;0.284490;,
     0.438244;0.284490;,
     0.438244;0.393099;,
     0.459204;0.393099;,
     0.631458;0.727672;,
     0.631458;0.727672;,
     0.964318;0.727672;,
     0.588140;0.007227;,
     0.582670;0.007227;,
     0.582670;0.457694;,
     0.588140;0.457694;,
     0.582670;0.457694;,
     0.588140;0.457694;,
     0.588140;0.007227;,
     0.582670;0.007227;,
     0.544616;0.085488;,
     0.544837;0.006505;,
     0.556608;0.018276;,
     0.556387;0.968630;,
     0.544616;0.980401;,
     0.588549;0.006394;,
     0.582370;0.012442;,
     0.582370;0.451874;,
     0.228248;0.004006;,
     0.228248;0.532266;,
     0.560707;0.980374;,
     0.572478;0.968603;,
     0.572478;0.018064;,
     0.560707;0.006293;,
     0.977381;0.714375;,
     0.977381;0.370318;,
     0.944589;0.248671;,
     0.991915;0.013431;,
     0.998126;0.013431;,
     0.998126;0.714375;,
     0.989490;0.714018;,
     0.989490;0.111942;,
     0.989490;0.009889;,
     0.998350;0.009889;,
     0.998350;0.714018;,
     0.417220;0.532266;,
     0.417220;0.004006;,
     0.424033;0.532266;,
     0.424033;0.004006;,
     0.528394;0.346152;,
     0.530983;0.336826;,
     0.494046;0.336826;,
     0.496634;0.346152;,
     0.186407;0.994696;,
     0.186407;0.934351;,
     0.222887;0.934351;,
     0.222887;0.994696;,
     0.487308;0.343563;,
     0.487308;0.385936;,
     0.496634;0.383348;,
     0.222865;0.994696;,
     0.222865;0.905912;,
     0.214034;0.905912;,
     0.202733;0.910709;,
     0.190968;0.922768;,
     0.186385;0.934351;,
     0.186385;0.994696;,
     0.494046;0.392674;,
     0.530983;0.392674;,
     0.528394;0.383348;,
     0.186407;0.994696;,
     0.186407;0.905912;,
     0.222887;0.905912;,
     0.222887;0.994696;,
     0.537720;0.385936;,
     0.537720;0.343563;,
     0.222909;0.994696;,
     0.222909;0.934351;,
     0.218326;0.922768;,
     0.206561;0.910709;,
     0.195260;0.905912;,
     0.186429;0.905912;,
     0.186429;0.994696;,
     0.186407;0.887653;,
     0.222887;0.887653;,
     0.222887;0.892223;,
     0.186407;0.892223;,
     0.186407;0.904760;,
     0.222887;0.904760;,
     0.222887;0.921814;,
     0.186407;0.921814;,
     0.944589;0.248671;,
     0.977381;0.370318;,
     0.977381;0.714375;,
     0.998126;0.714375;,
     0.998126;0.013431;,
     0.991915;0.013431;,
     0.129489;0.273533;,
     0.129489;0.485533;,
     0.134093;0.450200;,
     0.138696;0.414867;,
     0.138696;0.379533;,
     0.138696;0.344200;,
     0.989490;0.111942;,
     0.989490;0.009889;,
     0.984554;0.080679;,
     0.919624;0.015749;,
     0.888235;0.009889;,
     0.947849;0.030051;,
     0.970163;0.052454;,
     0.919624;0.015749;,
     0.888234;0.009889;,
     0.984554;0.080679;,
     0.970163;0.052454;,
     0.947849;0.030051;,
     0.582370;0.451874;,
     0.588549;0.457913;,
     0.588549;0.006394;,
     0.582370;0.012442;,
     0.631437;0.715543;,
     0.994474;0.715543;,
     0.994474;0.003946;,
     0.631437;0.003946;,
     0.989490;0.714018;,
     0.998350;0.714018;,
     0.998350;0.009889;,
     0.462159;0.097564;,
     0.525644;0.097564;,
     0.525644;0.018766;,
     0.462159;0.018766;,
     0.452897;0.256404;,
     0.525644;0.256404;,
     0.525644;0.177607;,
     0.452897;0.177607;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.709037;0.016695;,
     0.614057;0.016695;,
     0.614057;0.114700;,
     0.709037;0.114700;,
     0.614057;0.119364;,
     0.709037;0.119364;,
     0.614057;0.191436;,
     0.709037;0.191436;,
     0.709037;0.011938;,
     0.614057;0.011938;,
     0.709037;0.084010;,
     0.709037;0.088674;,
     0.614057;0.088674;,
     0.614057;0.084010;,
     0.709037;0.186866;,
     0.614057;0.186866;,
     0.155996;0.878799;,
     0.155996;0.847587;,
     0.179757;0.847587;,
     0.179757;0.878799;,
     0.173697;0.832244;,
     0.172916;0.828279;,
     0.155222;0.828279;,
     0.154441;0.832244;,
     0.154442;0.832244;,
     0.155224;0.828279;,
     0.172918;0.828279;,
     0.173699;0.832244;,
     0.155225;0.800229;,
     0.172919;0.800229;,
     0.172915;0.800229;,
     0.155221;0.800229;,
     0.121152;0.641922;,
     0.140006;0.641922;,
     0.140006;0.343922;,
     0.121152;0.343922;,
     0.583151;0.492910;,
     0.589674;0.492910;,
     0.589674;0.462083;,
     0.583151;0.462083;,
     0.589674;0.462083;,
     0.583151;0.462083;,
     0.583151;0.492910;,
     0.589674;0.492910;,
     0.544616;0.980401;,
     0.556387;0.968630;,
     0.556387;0.018091;,
     0.544616;0.006321;,
     0.544616;0.085488;,
     0.818624;0.392347;,
     0.717369;0.392347;,
     0.717369;0.467137;,
     0.818624;0.467137;,
     0.604386;0.076790;,
     0.601150;0.076790;,
     0.597914;0.076790;,
     0.597914;0.251175;,
     0.601150;0.251175;,
     0.604386;0.251175;,
     0.185558;0.884363;,
     0.200587;0.793143;,
     0.200587;0.672394;,
     0.185558;0.537741;,
     0.597914;0.073554;,
     0.601150;0.073554;,
     0.604386;0.073554;,
     0.604386;0.006832;,
     0.597914;0.006832;,
     0.597914;0.254411;,
     0.601150;0.254411;,
     0.200587;0.537741;,
     0.522669;0.537741;,
     0.410083;0.537741;,
     0.410083;0.884363;,
     0.522669;0.884363;,
     0.582370;0.012442;,
     0.582370;0.451874;,
     0.560707;0.006293;,
     0.572478;0.968603;,
     0.560707;0.980374;,
     0.717369;0.338407;,
     0.717369;0.521077;,
     0.818624;0.521077;,
     0.818624;0.338407;,
     0.604386;0.281728;,
     0.604386;0.254411;,
     0.597914;0.281728;,
     0.572478;0.018064;,
     0.582370;0.451874;,
     0.588549;0.457913;,
     0.588549;0.006394;,
     0.582370;0.012442;,
     0.631437;0.715543;,
     0.994474;0.715543;,
     0.994474;0.003946;,
     0.631437;0.003946;,
     0.308613;0.672394;,
     0.308613;0.793143;,
     0.297713;0.537741;,
     0.297713;0.672394;,
     0.308613;0.537741;,
     0.308613;0.884363;,
     0.200587;0.884363;,
     0.297713;0.793143;,
     0.297713;0.884363;,
     0.644840;0.321786;,
     0.767962;0.322548;,
     0.758006;0.236504;,
     0.644840;0.235743;,
     0.768372;0.148761;,
     0.644840;0.149522;,
     0.644840;0.235566;,
     0.758417;0.234805;,
     0.460091;0.257431;,
     0.471384;0.239615;,
     0.471384;0.197865;,
     0.450207;0.180050;,
     0.516737;0.197865;,
     0.527848;0.180050;,
     0.527848;0.257431;,
     0.516737;0.239615;,
     0.437763;0.402316;,
     0.514558;0.439585;,
     0.441971;0.398048;,
     0.525240;0.439585;,
     0.533721;0.398048;,
     0.437763;0.529266;,
     0.514558;0.464133;,
     0.441971;0.533534;,
     0.537929;0.402316;,
     0.525240;0.464133;,
     0.537929;0.529266;,
     0.533721;0.533534;,
     0.171333;0.851095;,
     0.171333;0.875290;,
     0.164420;0.875290;,
     0.164420;0.851095;,
     0.557359;0.006929;,
     0.544379;0.006929;,
     0.544379;0.081681;,
     0.544379;0.769761;,
     0.550869;0.769761;,
     0.557359;0.769761;,
     0.004468;0.003573;,
     0.004468;0.026000;,
     0.049859;0.026000;,
     0.049859;0.003573;,
     0.557359;0.980057;,
     0.557359;0.781075;,
     0.550869;0.781075;,
     0.544379;0.781075;,
     0.544379;0.971731;,
     0.589372;0.457994;,
     0.589372;0.006745;,
     0.581728;0.016330;,
     0.581728;0.457994;,
     0.004468;0.712421;,
     0.049859;0.712421;,
     0.049859;0.114911;,
     0.004468;0.114911;,
     0.550869;0.772148;,
     0.546292;0.775418;,
     0.550869;0.778687;,
     0.049859;0.092682;,
     0.004468;0.092682;,
     0.641348;0.110700;,
     0.641348;0.008648;,
     0.646285;0.079437;,
     0.004468;0.048223;,
     0.049859;0.048223;,
     0.004468;0.070431;,
     0.049859;0.070431;,
     0.682990;0.028810;,
     0.711215;0.014507;,
     0.660676;0.051212;,
     0.742604;0.008648;,
     0.581848;0.007085;,
     0.575417;0.007085;,
     0.575417;0.061372;,
     0.575417;0.067803;,
     0.575417;0.414349;,
     0.575417;0.420780;,
     0.575417;0.544051;,
     0.581848;0.544051;,
     0.560811;0.043079;,
     0.572707;0.049484;,
     0.572707;0.006402;,
     0.560811;0.012807;,
     0.556057;0.012750;,
     0.544161;0.006344;,
     0.544161;0.049427;,
     0.556057;0.043021;,
     0.590032;0.534214;,
     0.596122;0.545523;,
     0.596122;0.005587;,
     0.590032;0.016897;,
     0.560811;0.049484;,
     0.560811;0.772475;,
     0.560811;0.783642;,
     0.560811;0.980042;,
     0.572707;0.980042;,
     0.556057;0.049427;,
     0.556057;0.979984;,
     0.556057;0.783585;,
     0.556057;0.772417;,
     0.544161;0.979984;,
     0.175313;0.714626;,
     0.151242;0.710782;,
     0.111935;0.710782;,
     0.056902;0.714626;,
     0.615268;0.004475;,
     0.615268;0.662629;,
     0.624246;0.662629;,
     0.624246;0.004475;,
     0.615268;0.004475;,
     0.615268;0.662629;,
     0.624246;0.662629;,
     0.624246;0.004475;,
     0.430957;0.004006;,
     0.430957;0.532266;,
     0.432479;0.532266;,
     0.432479;0.404598;,
     0.432479;0.398406;,
     0.432479;0.074243;,
     0.432479;0.068051;,
     0.432479;0.004006;,
     0.157492;0.704295;,
     0.157945;0.322344;,
     0.074270;0.322344;,
     0.105685;0.704295;,
     0.105685;0.668729;,
     0.111935;0.662241;,
     0.151242;0.662241;,
     0.157492;0.668729;,
     0.056902;0.110439;,
     0.074270;0.202641;,
     0.157945;0.202641;,
     0.175313;0.110439;,
     0.175313;0.089143;,
     0.056902;0.089143;,
     0.175313;0.025260;,
     0.175313;0.003774;,
     0.056902;0.003774;,
     0.056902;0.025260;,
     0.175313;0.046550;,
     0.056902;0.046550;,
     0.175313;0.067825;,
     0.056902;0.067825;,
     0.581731;0.457841;,
     0.581731;0.015995;,
     0.589374;0.006595;,
     0.589376;0.457660;,
     0.544475;0.006929;,
     0.544475;0.769761;,
     0.550965;0.769761;,
     0.557455;0.769761;,
     0.557455;0.081681;,
     0.557455;0.006929;,
     0.742604;0.008648;,
     0.641348;0.008648;,
     0.711215;0.014507;,
     0.557455;0.781075;,
     0.550965;0.781075;,
     0.544475;0.781075;,
     0.544475;0.980057;,
     0.557455;0.971731;,
     0.550965;0.778687;,
     0.555542;0.775418;,
     0.550965;0.772148;,
     0.646285;0.079437;,
     0.641348;0.110700;,
     0.660676;0.051212;,
     0.682990;0.028810;,
     0.064262;0.451389;,
     0.064262;0.348537;,
     0.169270;0.348537;,
     0.169270;0.451389;,
     0.588549;0.457913;,
     0.413378;0.532266;,
     0.413378;0.004006;,
     0.414697;0.004006;,
     0.414697;0.532266;,
     0.059747;0.462034;,
     0.064262;0.451389;,
     0.064262;0.348537;,
     0.059747;0.330034;,
     0.173639;0.330034;,
     0.169270;0.348537;,
     0.169270;0.451389;,
     0.173639;0.462034;,
     0.064262;0.348537;,
     0.067964;0.352239;,
     0.165567;0.352239;,
     0.169270;0.348537;,
     0.169270;0.451389;,
     0.165567;0.447687;,
     0.067964;0.447687;,
     0.064262;0.451389;,
     0.160989;0.878799;,
     0.160989;0.847587;,
     0.174764;0.847587;,
     0.174764;0.878799;,
     0.588549;0.457913;,
     0.588549;0.006394;,
     0.560707;0.006293;,
     0.452897;0.177607;,
     0.525644;0.177607;,
     0.525644;0.097564;,
     0.462159;0.097564;,
     0.174764;0.847587;,
     0.174764;0.878799;,
     0.160989;0.878799;,
     0.160989;0.847587;,
     0.424033;0.004006;,
     0.424033;0.532266;;
    }

    VertexDuplicationIndices {
     655;
     613;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31,
     32,
     33,
     34,
     35,
     36,
     37,
     38,
     39,
     40,
     41,
     42,
     43,
     44,
     45,
     46,
     47,
     48,
     49,
     50,
     51,
     52,
     53,
     54,
     55,
     56,
     57,
     58,
     59,
     60,
     61,
     62,
     63,
     64,
     65,
     66,
     67,
     68,
     69,
     70,
     71,
     72,
     73,
     74,
     75,
     76,
     77,
     78,
     79,
     80,
     81,
     82,
     83,
     84,
     85,
     86,
     87,
     88,
     89,
     90,
     91,
     92,
     93,
     94,
     95,
     96,
     97,
     98,
     99,
     100,
     101,
     102,
     103,
     104,
     105,
     106,
     107,
     108,
     109,
     110,
     111,
     112,
     113,
     114,
     115,
     116,
     117,
     118,
     119,
     120,
     121,
     122,
     123,
     124,
     125,
     126,
     127,
     128,
     129,
     130,
     131,
     132,
     133,
     134,
     135,
     136,
     137,
     138,
     139,
     140,
     141,
     142,
     143,
     144,
     145,
     146,
     147,
     148,
     149,
     150,
     151,
     152,
     153,
     154,
     155,
     156,
     157,
     158,
     159,
     160,
     161,
     162,
     163,
     164,
     165,
     166,
     167,
     168,
     169,
     170,
     171,
     172,
     173,
     174,
     175,
     176,
     177,
     178,
     179,
     180,
     181,
     182,
     183,
     184,
     185,
     186,
     187,
     188,
     189,
     190,
     191,
     192,
     193,
     194,
     195,
     196,
     197,
     198,
     199,
     200,
     201,
     202,
     203,
     204,
     205,
     206,
     207,
     208,
     209,
     210,
     211,
     212,
     213,
     214,
     215,
     216,
     217,
     218,
     219,
     220,
     221,
     222,
     223,
     224,
     225,
     226,
     227,
     228,
     229,
     230,
     231,
     232,
     233,
     234,
     235,
     236,
     237,
     238,
     239,
     240,
     241,
     242,
     243,
     244,
     245,
     246,
     247,
     248,
     249,
     250,
     251,
     252,
     253,
     254,
     255,
     256,
     257,
     258,
     259,
     260,
     261,
     262,
     263,
     264,
     265,
     266,
     267,
     268,
     269,
     270,
     271,
     272,
     273,
     274,
     275,
     276,
     277,
     278,
     279,
     280,
     281,
     282,
     283,
     284,
     285,
     286,
     287,
     288,
     289,
     290,
     291,
     292,
     293,
     294,
     295,
     296,
     297,
     298,
     299,
     300,
     301,
     302,
     303,
     304,
     305,
     306,
     307,
     308,
     309,
     310,
     311,
     312,
     313,
     314,
     315,
     316,
     317,
     318,
     319,
     320,
     321,
     322,
     323,
     324,
     325,
     326,
     327,
     328,
     329,
     330,
     331,
     332,
     333,
     334,
     335,
     336,
     337,
     338,
     339,
     340,
     341,
     342,
     343,
     344,
     345,
     346,
     347,
     348,
     349,
     350,
     351,
     352,
     353,
     354,
     355,
     356,
     357,
     358,
     359,
     360,
     361,
     362,
     363,
     364,
     365,
     366,
     367,
     368,
     369,
     370,
     371,
     372,
     373,
     374,
     375,
     376,
     377,
     378,
     379,
     380,
     381,
     382,
     383,
     384,
     385,
     386,
     387,
     388,
     389,
     390,
     391,
     392,
     393,
     394,
     395,
     396,
     397,
     398,
     399,
     400,
     401,
     402,
     403,
     404,
     405,
     406,
     407,
     408,
     409,
     410,
     411,
     412,
     413,
     414,
     415,
     416,
     417,
     418,
     419,
     420,
     421,
     422,
     423,
     424,
     425,
     426,
     427,
     428,
     429,
     430,
     431,
     432,
     433,
     434,
     435,
     436,
     437,
     438,
     439,
     440,
     441,
     442,
     443,
     444,
     445,
     446,
     447,
     448,
     449,
     450,
     451,
     452,
     453,
     454,
     455,
     456,
     457,
     458,
     459,
     460,
     461,
     462,
     463,
     464,
     465,
     466,
     467,
     468,
     469,
     470,
     471,
     472,
     473,
     474,
     475,
     476,
     477,
     478,
     479,
     480,
     481,
     482,
     483,
     484,
     485,
     486,
     487,
     488,
     489,
     490,
     491,
     492,
     493,
     494,
     495,
     496,
     497,
     498,
     499,
     500,
     501,
     502,
     503,
     504,
     505,
     506,
     507,
     508,
     509,
     510,
     511,
     512,
     513,
     514,
     515,
     516,
     517,
     518,
     519,
     520,
     521,
     522,
     523,
     524,
     525,
     526,
     527,
     528,
     529,
     530,
     531,
     532,
     533,
     534,
     535,
     536,
     537,
     538,
     539,
     540,
     541,
     542,
     543,
     544,
     545,
     546,
     547,
     548,
     549,
     550,
     551,
     552,
     553,
     554,
     555,
     556,
     557,
     558,
     559,
     560,
     561,
     562,
     563,
     564,
     565,
     566,
     567,
     568,
     569,
     570,
     571,
     572,
     573,
     574,
     575,
     576,
     577,
     578,
     579,
     580,
     581,
     582,
     583,
     584,
     585,
     586,
     587,
     588,
     589,
     590,
     591,
     592,
     593,
     594,
     595,
     596,
     597,
     598,
     599,
     600,
     601,
     602,
     603,
     604,
     605,
     606,
     607,
     608,
     609,
     610,
     611,
     612,
     74,
     65,
     66,
     73,
     57,
     18,
     21,
     20,
     19,
     75,
     74,
     65,
     64,
     67,
     66,
     73,
     72,
     65,
     81,
     82,
     66,
     73,
     83,
     80,
     74,
     165,
     161,
     162,
     164,
     58,
     54,
     417,
     335,
     334,
     329,
     328,
     162,
     164,
     165,
     161,
     250,
     249;
    }

    MeshMaterialList {
     1;
     483;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_z {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.149213;,
    0.248240;0.719102;-0.133354;,
    0.244095;0.729758;-0.133354;,
    0.244095;0.729758;-0.149213;,
    0.250004;0.716925;-0.136023;,
    0.250004;0.716925;-0.146545;,
    0.243924;0.732555;-0.146545;,
    0.243924;0.732555;-0.136023;,
    0.232819;0.708604;-0.148069;,
    0.250004;0.716925;-0.146545;,
    0.248240;0.719102;-0.149213;,
    0.231852;0.711091;-0.150738;,
    0.244095;0.729758;-0.149213;,
    0.226601;0.724588;-0.150738;,
    0.248240;0.719102;-0.133354;,
    0.231852;0.711091;-0.131830;,
    0.226601;0.724588;-0.131830;,
    0.244095;0.729758;-0.133354;,
    0.232819;0.708604;-0.134499;,
    0.250004;0.716925;-0.136023;,
    0.250004;0.716925;-0.146545;,
    0.232819;0.708604;-0.148069;,
    0.250004;0.716925;-0.136023;,
    0.232819;0.708604;-0.134499;,
    0.225633;0.727075;-0.148069;,
    0.243924;0.732555;-0.146545;,
    0.243924;0.732555;-0.136023;,
    0.225633;0.727075;-0.134499;,
    0.243924;0.732555;-0.146545;,
    0.225633;0.727075;-0.148069;,
    0.225633;0.727075;-0.134499;,
    0.243924;0.732555;-0.136023;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.988654;,
     0.110939;0.965183;,
     0.093767;0.965183;,
     0.093766;0.988654;,
     0.115148;0.969191;,
     0.115148;0.984646;,
     0.089558;0.984646;,
     0.089558;0.969191;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_hyphen {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;0.004269;,
    0.248240;0.719102;0.020128;,
    0.244095;0.729758;0.020128;,
    0.244095;0.729758;0.004269;,
    0.250004;0.716925;0.017459;,
    0.250004;0.716925;0.006937;,
    0.243924;0.732555;0.006937;,
    0.243924;0.732555;0.017459;,
    0.232819;0.708604;0.005413;,
    0.250004;0.716925;0.006937;,
    0.248240;0.719102;0.004269;,
    0.231852;0.711091;0.002744;,
    0.244095;0.729758;0.004269;,
    0.226601;0.724588;0.002744;,
    0.248240;0.719102;0.020128;,
    0.231852;0.711091;0.021652;,
    0.226601;0.724588;0.021652;,
    0.244095;0.729758;0.020128;,
    0.232819;0.708604;0.018984;,
    0.250004;0.716925;0.017459;,
    0.250004;0.716925;0.006937;,
    0.232819;0.708604;0.005413;,
    0.250004;0.716925;0.017459;,
    0.232819;0.708604;0.018984;,
    0.225633;0.727075;0.005413;,
    0.243924;0.732555;0.006937;,
    0.243924;0.732555;0.017459;,
    0.225633;0.727075;0.018984;,
    0.243924;0.732555;0.006937;,
    0.225633;0.727075;0.005413;,
    0.225633;0.727075;0.018984;,
    0.243924;0.732555;0.017459;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.804118;,
     0.110939;0.780648;,
     0.093767;0.780648;,
     0.093766;0.804118;,
     0.115148;0.784656;,
     0.115148;0.800110;,
     0.089558;0.800110;,
     0.089558;0.784656;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_x {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.127287;,
    0.248240;0.719102;-0.111428;,
    0.244095;0.729758;-0.111428;,
    0.244095;0.729758;-0.127287;,
    0.250004;0.716925;-0.114097;,
    0.250004;0.716925;-0.124619;,
    0.243924;0.732555;-0.124619;,
    0.243924;0.732555;-0.114097;,
    0.232819;0.708604;-0.126143;,
    0.250004;0.716925;-0.124619;,
    0.248240;0.719102;-0.127287;,
    0.231852;0.711091;-0.128812;,
    0.244095;0.729758;-0.127287;,
    0.226601;0.724588;-0.128812;,
    0.248240;0.719102;-0.111428;,
    0.231852;0.711091;-0.109904;,
    0.226601;0.724588;-0.109904;,
    0.244095;0.729758;-0.111428;,
    0.232819;0.708604;-0.112572;,
    0.250004;0.716925;-0.114097;,
    0.250004;0.716925;-0.124619;,
    0.232819;0.708604;-0.126143;,
    0.250004;0.716925;-0.114097;,
    0.232819;0.708604;-0.112572;,
    0.225633;0.727075;-0.126143;,
    0.243924;0.732555;-0.124619;,
    0.243924;0.732555;-0.114097;,
    0.225633;0.727075;-0.112572;,
    0.243924;0.732555;-0.124619;,
    0.225633;0.727075;-0.126143;,
    0.225633;0.727075;-0.112572;,
    0.243924;0.732555;-0.114097;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.961536;,
     0.110939;0.938065;,
     0.093767;0.938065;,
     0.093766;0.961536;,
     0.115148;0.942073;,
     0.115148;0.957528;,
     0.089558;0.957528;,
     0.089558;0.942073;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_a {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.149213;,
    0.240249;0.739642;-0.133354;,
    0.236103;0.750298;-0.133354;,
    0.236103;0.750298;-0.149213;,
    0.242013;0.737465;-0.136023;,
    0.242013;0.737465;-0.146545;,
    0.235933;0.753095;-0.146545;,
    0.235933;0.753095;-0.136023;,
    0.224828;0.729144;-0.148069;,
    0.242013;0.737465;-0.146545;,
    0.240249;0.739642;-0.149213;,
    0.223861;0.731631;-0.150738;,
    0.236103;0.750298;-0.149213;,
    0.218610;0.745128;-0.150738;,
    0.240249;0.739642;-0.133354;,
    0.223861;0.731631;-0.131830;,
    0.218610;0.745128;-0.131830;,
    0.236103;0.750298;-0.133354;,
    0.224828;0.729144;-0.134499;,
    0.242013;0.737465;-0.136023;,
    0.242013;0.737465;-0.146545;,
    0.224828;0.729144;-0.148069;,
    0.242013;0.737465;-0.136023;,
    0.224828;0.729144;-0.134499;,
    0.217642;0.747615;-0.148069;,
    0.235933;0.753095;-0.146545;,
    0.235933;0.753095;-0.136023;,
    0.217642;0.747615;-0.134499;,
    0.235933;0.753095;-0.146545;,
    0.217642;0.747615;-0.148069;,
    0.217642;0.747615;-0.134499;,
    0.235933;0.753095;-0.136023;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.988419;,
     0.083202;0.964949;,
     0.066029;0.964949;,
     0.066029;0.988419;,
     0.087410;0.968957;,
     0.087410;0.984411;,
     0.061820;0.984411;,
     0.061820;0.968957;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_k {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;0.004269;,
    0.240249;0.739642;0.020128;,
    0.236103;0.750298;0.020128;,
    0.236103;0.750298;0.004269;,
    0.242013;0.737465;0.017459;,
    0.242013;0.737465;0.006937;,
    0.235933;0.753095;0.006937;,
    0.235933;0.753095;0.017459;,
    0.224828;0.729144;0.005413;,
    0.242013;0.737465;0.006937;,
    0.240249;0.739642;0.004269;,
    0.223861;0.731631;0.002744;,
    0.236103;0.750298;0.004269;,
    0.218610;0.745128;0.002744;,
    0.240249;0.739642;0.020128;,
    0.223861;0.731631;0.021652;,
    0.218610;0.745128;0.021652;,
    0.236103;0.750298;0.020128;,
    0.224828;0.729144;0.018984;,
    0.242013;0.737465;0.017459;,
    0.242013;0.737465;0.006937;,
    0.224828;0.729144;0.005413;,
    0.242013;0.737465;0.017459;,
    0.224828;0.729144;0.018984;,
    0.217642;0.747615;0.005413;,
    0.235933;0.753095;0.006937;,
    0.235933;0.753095;0.017459;,
    0.217642;0.747615;0.018984;,
    0.235933;0.753095;0.006937;,
    0.217642;0.747615;0.005413;,
    0.217642;0.747615;0.018984;,
    0.235933;0.753095;0.017459;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.803884;,
     0.083202;0.780413;,
     0.066029;0.780413;,
     0.066029;0.803884;,
     0.087410;0.784421;,
     0.087410;0.799875;,
     0.061820;0.799875;,
     0.061820;0.784421;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_s {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.127287;,
    0.240249;0.739642;-0.111428;,
    0.236103;0.750298;-0.111428;,
    0.236103;0.750298;-0.127287;,
    0.242013;0.737465;-0.114097;,
    0.242013;0.737465;-0.124619;,
    0.235933;0.753095;-0.124619;,
    0.235933;0.753095;-0.114097;,
    0.224828;0.729144;-0.126143;,
    0.242013;0.737465;-0.124619;,
    0.240249;0.739642;-0.127287;,
    0.223861;0.731631;-0.128812;,
    0.236103;0.750298;-0.127287;,
    0.218610;0.745128;-0.128812;,
    0.240249;0.739642;-0.111428;,
    0.223861;0.731631;-0.109904;,
    0.218610;0.745128;-0.109904;,
    0.236103;0.750298;-0.111428;,
    0.224828;0.729144;-0.112572;,
    0.242013;0.737465;-0.114097;,
    0.242013;0.737465;-0.124619;,
    0.224828;0.729144;-0.126143;,
    0.242013;0.737465;-0.114097;,
    0.224828;0.729144;-0.112572;,
    0.217642;0.747615;-0.126143;,
    0.235933;0.753095;-0.124619;,
    0.235933;0.753095;-0.114097;,
    0.217642;0.747615;-0.112572;,
    0.235933;0.753095;-0.124619;,
    0.217642;0.747615;-0.126143;,
    0.217642;0.747615;-0.112572;,
    0.235933;0.753095;-0.114097;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.961301;,
     0.083202;0.937830;,
     0.066029;0.937830;,
     0.066029;0.961301;,
     0.087410;0.941838;,
     0.087410;0.957293;,
     0.061820;0.957293;,
     0.061820;0.941838;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_q {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.149213;,
    0.232258;0.760183;-0.133354;,
    0.228112;0.770838;-0.133354;,
    0.228112;0.770838;-0.149213;,
    0.234022;0.758006;-0.136023;,
    0.234022;0.758006;-0.146545;,
    0.227941;0.773635;-0.146545;,
    0.227941;0.773635;-0.136023;,
    0.216837;0.749684;-0.148069;,
    0.234022;0.758006;-0.146545;,
    0.232258;0.760183;-0.149213;,
    0.215870;0.752171;-0.150738;,
    0.228112;0.770838;-0.149213;,
    0.210619;0.765668;-0.150738;,
    0.232258;0.760183;-0.133354;,
    0.215870;0.752171;-0.131830;,
    0.210619;0.765668;-0.131830;,
    0.228112;0.770838;-0.133354;,
    0.216837;0.749684;-0.134499;,
    0.234022;0.758006;-0.136023;,
    0.234022;0.758006;-0.146545;,
    0.216837;0.749684;-0.148069;,
    0.234022;0.758006;-0.136023;,
    0.216837;0.749684;-0.134499;,
    0.209651;0.768155;-0.148069;,
    0.227941;0.773635;-0.146545;,
    0.227941;0.773635;-0.136023;,
    0.209651;0.768155;-0.134499;,
    0.227941;0.773635;-0.146545;,
    0.209651;0.768155;-0.148069;,
    0.209651;0.768155;-0.134499;,
    0.227941;0.773635;-0.136023;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.989527;,
     0.056243;0.966056;,
     0.039071;0.966056;,
     0.039070;0.989527;,
     0.060452;0.970064;,
     0.060452;0.985519;,
     0.034862;0.985519;,
     0.034862;0.970064;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_i {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;0.004269;,
    0.232258;0.760183;0.020128;,
    0.228112;0.770838;0.020128;,
    0.228112;0.770838;0.004269;,
    0.234022;0.758006;0.017459;,
    0.234022;0.758006;0.006937;,
    0.227941;0.773635;0.006937;,
    0.227941;0.773635;0.017459;,
    0.216837;0.749684;0.005413;,
    0.234022;0.758006;0.006937;,
    0.232258;0.760183;0.004269;,
    0.215870;0.752171;0.002744;,
    0.228112;0.770838;0.004269;,
    0.210619;0.765668;0.002744;,
    0.232258;0.760183;0.020128;,
    0.215870;0.752171;0.021652;,
    0.210619;0.765668;0.021652;,
    0.228112;0.770838;0.020128;,
    0.216837;0.749684;0.018984;,
    0.234022;0.758006;0.017459;,
    0.234022;0.758006;0.006937;,
    0.216837;0.749684;0.005413;,
    0.234022;0.758006;0.017459;,
    0.216837;0.749684;0.018984;,
    0.209651;0.768155;0.005413;,
    0.227941;0.773635;0.006937;,
    0.227941;0.773635;0.017459;,
    0.209651;0.768155;0.018984;,
    0.227941;0.773635;0.006937;,
    0.209651;0.768155;0.005413;,
    0.209651;0.768155;0.018984;,
    0.227941;0.773635;0.017459;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.804991;,
     0.056243;0.781520;,
     0.039071;0.781520;,
     0.039070;0.804991;,
     0.060452;0.785528;,
     0.060452;0.800983;,
     0.034862;0.800983;,
     0.034862;0.785528;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_w {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.127287;,
    0.232258;0.760183;-0.111428;,
    0.228112;0.770838;-0.111428;,
    0.228112;0.770838;-0.127287;,
    0.234022;0.758006;-0.114097;,
    0.234022;0.758006;-0.124619;,
    0.227941;0.773635;-0.124619;,
    0.227941;0.773635;-0.114097;,
    0.216837;0.749684;-0.126143;,
    0.234022;0.758006;-0.124619;,
    0.232258;0.760183;-0.127287;,
    0.215870;0.752171;-0.128812;,
    0.228112;0.770838;-0.127287;,
    0.210619;0.765668;-0.128812;,
    0.232258;0.760183;-0.111428;,
    0.215870;0.752171;-0.109904;,
    0.210619;0.765668;-0.109904;,
    0.228112;0.770838;-0.111428;,
    0.216837;0.749684;-0.112572;,
    0.234022;0.758006;-0.114097;,
    0.234022;0.758006;-0.124619;,
    0.216837;0.749684;-0.126143;,
    0.234022;0.758006;-0.114097;,
    0.216837;0.749684;-0.112572;,
    0.209651;0.768155;-0.126143;,
    0.227941;0.773635;-0.124619;,
    0.227941;0.773635;-0.114097;,
    0.209651;0.768155;-0.112572;,
    0.227941;0.773635;-0.124619;,
    0.209651;0.768155;-0.126143;,
    0.209651;0.768155;-0.112572;,
    0.227941;0.773635;-0.114097;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.962409;,
     0.056243;0.938938;,
     0.039071;0.938938;,
     0.039070;0.962409;,
     0.060452;0.942946;,
     0.060452;0.958401;,
     0.034862;0.958401;,
     0.034862;0.942946;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_1 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.149213;,
    0.224267;0.780723;-0.133354;,
    0.220121;0.791379;-0.133354;,
    0.220121;0.791379;-0.149213;,
    0.226031;0.778546;-0.136023;,
    0.226031;0.778546;-0.146545;,
    0.219950;0.794176;-0.146545;,
    0.219950;0.794176;-0.136023;,
    0.208846;0.770225;-0.148069;,
    0.226031;0.778546;-0.146545;,
    0.224267;0.780723;-0.149213;,
    0.207879;0.772712;-0.150738;,
    0.220121;0.791379;-0.149213;,
    0.202628;0.786209;-0.150738;,
    0.224267;0.780723;-0.133354;,
    0.207879;0.772712;-0.131830;,
    0.202628;0.786209;-0.131830;,
    0.220121;0.791379;-0.133354;,
    0.208846;0.770225;-0.134499;,
    0.226031;0.778546;-0.136023;,
    0.226031;0.778546;-0.146545;,
    0.208846;0.770225;-0.148069;,
    0.226031;0.778546;-0.136023;,
    0.208846;0.770225;-0.134499;,
    0.201660;0.788696;-0.148069;,
    0.219950;0.794176;-0.146545;,
    0.219950;0.794176;-0.136023;,
    0.201660;0.788696;-0.134499;,
    0.219950;0.794176;-0.146545;,
    0.201660;0.788696;-0.148069;,
    0.201660;0.788696;-0.134499;,
    0.219950;0.794176;-0.136023;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.988798;,
     0.028511;0.965327;,
     0.011339;0.965327;,
     0.011338;0.988798;,
     0.032720;0.969335;,
     0.032720;0.984790;,
     0.007130;0.984790;,
     0.007130;0.969335;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_8 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;0.004269;,
    0.224267;0.780723;0.020128;,
    0.220121;0.791379;0.020128;,
    0.220121;0.791379;0.004269;,
    0.226031;0.778546;0.017459;,
    0.226031;0.778546;0.006937;,
    0.219950;0.794176;0.006937;,
    0.219950;0.794176;0.017459;,
    0.208846;0.770225;0.005413;,
    0.226031;0.778546;0.006937;,
    0.224267;0.780723;0.004269;,
    0.207879;0.772712;0.002744;,
    0.220121;0.791379;0.004269;,
    0.202628;0.786209;0.002744;,
    0.224267;0.780723;0.020128;,
    0.207879;0.772712;0.021652;,
    0.202628;0.786209;0.021652;,
    0.220121;0.791379;0.020128;,
    0.208846;0.770225;0.018984;,
    0.226031;0.778546;0.017459;,
    0.226031;0.778546;0.006937;,
    0.208846;0.770225;0.005413;,
    0.226031;0.778546;0.017459;,
    0.208846;0.770225;0.018984;,
    0.201660;0.788696;0.005413;,
    0.219950;0.794176;0.006937;,
    0.219950;0.794176;0.017459;,
    0.201660;0.788696;0.018984;,
    0.219950;0.794176;0.006937;,
    0.201660;0.788696;0.005413;,
    0.201660;0.788696;0.018984;,
    0.219950;0.794176;0.017459;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.804262;,
     0.028511;0.780792;,
     0.011339;0.780792;,
     0.011338;0.804262;,
     0.032720;0.784800;,
     0.032720;0.800254;,
     0.007130;0.800254;,
     0.007130;0.784800;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_2 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.127287;,
    0.224267;0.780723;-0.111428;,
    0.220121;0.791379;-0.111428;,
    0.220121;0.791379;-0.127287;,
    0.226031;0.778546;-0.114097;,
    0.226031;0.778546;-0.124619;,
    0.219950;0.794176;-0.124619;,
    0.219950;0.794176;-0.114097;,
    0.208846;0.770225;-0.126143;,
    0.226031;0.778546;-0.124619;,
    0.224267;0.780723;-0.127287;,
    0.207879;0.772712;-0.128812;,
    0.220121;0.791379;-0.127287;,
    0.202628;0.786209;-0.128812;,
    0.224267;0.780723;-0.111428;,
    0.207879;0.772712;-0.109904;,
    0.202628;0.786209;-0.109904;,
    0.220121;0.791379;-0.111428;,
    0.208846;0.770225;-0.112572;,
    0.226031;0.778546;-0.114097;,
    0.226031;0.778546;-0.124619;,
    0.208846;0.770225;-0.126143;,
    0.226031;0.778546;-0.114097;,
    0.208846;0.770225;-0.112572;,
    0.201660;0.788696;-0.126143;,
    0.219950;0.794176;-0.124619;,
    0.219950;0.794176;-0.114097;,
    0.201660;0.788696;-0.112572;,
    0.219950;0.794176;-0.124619;,
    0.201660;0.788696;-0.126143;,
    0.201660;0.788696;-0.112572;,
    0.219950;0.794176;-0.114097;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.961680;,
     0.028511;0.938209;,
     0.011339;0.938209;,
     0.011338;0.961680;,
     0.032720;0.942217;,
     0.032720;0.957672;,
     0.007130;0.957672;,
     0.007130;0.942217;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_3 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.105361;,
    0.224267;0.780723;-0.089502;,
    0.220121;0.791379;-0.089502;,
    0.220121;0.791379;-0.105361;,
    0.226031;0.778546;-0.092171;,
    0.226031;0.778546;-0.102693;,
    0.219950;0.794176;-0.102693;,
    0.219950;0.794176;-0.092171;,
    0.208846;0.770225;-0.104217;,
    0.226031;0.778546;-0.102693;,
    0.224267;0.780723;-0.105361;,
    0.207879;0.772712;-0.106886;,
    0.220121;0.791379;-0.105361;,
    0.202628;0.786209;-0.106886;,
    0.224267;0.780723;-0.089502;,
    0.207879;0.772712;-0.087978;,
    0.202628;0.786209;-0.087978;,
    0.220121;0.791379;-0.089502;,
    0.208846;0.770225;-0.090646;,
    0.226031;0.778546;-0.092171;,
    0.226031;0.778546;-0.102693;,
    0.208846;0.770225;-0.104217;,
    0.226031;0.778546;-0.092171;,
    0.208846;0.770225;-0.090646;,
    0.201660;0.788696;-0.104217;,
    0.219950;0.794176;-0.102693;,
    0.219950;0.794176;-0.092171;,
    0.201660;0.788696;-0.090646;,
    0.219950;0.794176;-0.102693;,
    0.201660;0.788696;-0.104217;,
    0.201660;0.788696;-0.090646;,
    0.219950;0.794176;-0.092171;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.936169;,
     0.028511;0.912698;,
     0.011339;0.912698;,
     0.011338;0.936169;,
     0.032720;0.916706;,
     0.032720;0.932161;,
     0.007130;0.932161;,
     0.007130;0.916706;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_4 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.083435;,
    0.224267;0.780723;-0.067576;,
    0.220121;0.791379;-0.067576;,
    0.220121;0.791379;-0.083435;,
    0.226031;0.778546;-0.070245;,
    0.226031;0.778546;-0.080767;,
    0.219950;0.794176;-0.080767;,
    0.219950;0.794176;-0.070245;,
    0.208846;0.770225;-0.082291;,
    0.226031;0.778546;-0.080767;,
    0.224267;0.780723;-0.083435;,
    0.207879;0.772712;-0.084960;,
    0.220121;0.791379;-0.083435;,
    0.202628;0.786209;-0.084960;,
    0.224267;0.780723;-0.067576;,
    0.207879;0.772712;-0.066052;,
    0.202628;0.786209;-0.066052;,
    0.220121;0.791379;-0.067576;,
    0.208846;0.770225;-0.068720;,
    0.226031;0.778546;-0.070245;,
    0.226031;0.778546;-0.080767;,
    0.208846;0.770225;-0.082291;,
    0.226031;0.778546;-0.070245;,
    0.208846;0.770225;-0.068720;,
    0.201660;0.788696;-0.082291;,
    0.219950;0.794176;-0.080767;,
    0.219950;0.794176;-0.070245;,
    0.201660;0.788696;-0.068720;,
    0.219950;0.794176;-0.080767;,
    0.201660;0.788696;-0.082291;,
    0.201660;0.788696;-0.068720;,
    0.219950;0.794176;-0.070245;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.909292;,
     0.028511;0.885821;,
     0.011339;0.885821;,
     0.011338;0.909292;,
     0.032720;0.889829;,
     0.032720;0.905284;,
     0.007130;0.905284;,
     0.007130;0.889829;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_5 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.061509;,
    0.224267;0.780723;-0.045650;,
    0.220121;0.791379;-0.045650;,
    0.220121;0.791379;-0.061509;,
    0.226031;0.778546;-0.048319;,
    0.226031;0.778546;-0.058841;,
    0.219950;0.794176;-0.058841;,
    0.219950;0.794176;-0.048319;,
    0.208846;0.770225;-0.060365;,
    0.226031;0.778546;-0.058841;,
    0.224267;0.780723;-0.061509;,
    0.207879;0.772712;-0.063034;,
    0.220121;0.791379;-0.061509;,
    0.202628;0.786209;-0.063034;,
    0.224267;0.780723;-0.045650;,
    0.207879;0.772712;-0.044126;,
    0.202628;0.786209;-0.044126;,
    0.220121;0.791379;-0.045650;,
    0.208846;0.770225;-0.046794;,
    0.226031;0.778546;-0.048319;,
    0.226031;0.778546;-0.058841;,
    0.208846;0.770225;-0.060365;,
    0.226031;0.778546;-0.048319;,
    0.208846;0.770225;-0.046794;,
    0.201660;0.788696;-0.060365;,
    0.219950;0.794176;-0.058841;,
    0.219950;0.794176;-0.048319;,
    0.201660;0.788696;-0.046794;,
    0.219950;0.794176;-0.058841;,
    0.201660;0.788696;-0.060365;,
    0.201660;0.788696;-0.046794;,
    0.219950;0.794176;-0.048319;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.883279;,
     0.028511;0.859808;,
     0.011339;0.859808;,
     0.011338;0.883279;,
     0.032720;0.863816;,
     0.032720;0.879271;,
     0.007130;0.879271;,
     0.007130;0.863816;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_6 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.039583;,
    0.224267;0.780723;-0.023724;,
    0.220121;0.791379;-0.023724;,
    0.220121;0.791379;-0.039583;,
    0.226031;0.778546;-0.026393;,
    0.226031;0.778546;-0.036915;,
    0.219950;0.794176;-0.036915;,
    0.219950;0.794176;-0.026393;,
    0.208846;0.770225;-0.038439;,
    0.226031;0.778546;-0.036915;,
    0.224267;0.780723;-0.039583;,
    0.207879;0.772712;-0.041108;,
    0.220121;0.791379;-0.039583;,
    0.202628;0.786209;-0.041108;,
    0.224267;0.780723;-0.023724;,
    0.207879;0.772712;-0.022200;,
    0.202628;0.786209;-0.022200;,
    0.220121;0.791379;-0.023724;,
    0.208846;0.770225;-0.024868;,
    0.226031;0.778546;-0.026393;,
    0.226031;0.778546;-0.036915;,
    0.208846;0.770225;-0.038439;,
    0.226031;0.778546;-0.026393;,
    0.208846;0.770225;-0.024868;,
    0.201660;0.788696;-0.038439;,
    0.219950;0.794176;-0.036915;,
    0.219950;0.794176;-0.026393;,
    0.201660;0.788696;-0.024868;,
    0.219950;0.794176;-0.036915;,
    0.201660;0.788696;-0.038439;,
    0.201660;0.788696;-0.024868;,
    0.219950;0.794176;-0.026393;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.856625;,
     0.028511;0.833154;,
     0.011339;0.833154;,
     0.011338;0.856625;,
     0.032720;0.837162;,
     0.032720;0.852617;,
     0.007130;0.852617;,
     0.007130;0.837162;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_7 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;-0.017657;,
    0.224267;0.780723;-0.001798;,
    0.220121;0.791379;-0.001798;,
    0.220121;0.791379;-0.017657;,
    0.226031;0.778546;-0.004467;,
    0.226031;0.778546;-0.014989;,
    0.219950;0.794176;-0.014989;,
    0.219950;0.794176;-0.004467;,
    0.208846;0.770225;-0.016513;,
    0.226031;0.778546;-0.014989;,
    0.224267;0.780723;-0.017657;,
    0.207879;0.772712;-0.019182;,
    0.220121;0.791379;-0.017657;,
    0.202628;0.786209;-0.019182;,
    0.224267;0.780723;-0.001798;,
    0.207879;0.772712;-0.000274;,
    0.202628;0.786209;-0.000274;,
    0.220121;0.791379;-0.001798;,
    0.208846;0.770225;-0.002942;,
    0.226031;0.778546;-0.004467;,
    0.226031;0.778546;-0.014989;,
    0.208846;0.770225;-0.016513;,
    0.226031;0.778546;-0.004467;,
    0.208846;0.770225;-0.002942;,
    0.201660;0.788696;-0.016513;,
    0.219950;0.794176;-0.014989;,
    0.219950;0.794176;-0.004467;,
    0.201660;0.788696;-0.002942;,
    0.219950;0.794176;-0.014989;,
    0.201660;0.788696;-0.016513;,
    0.201660;0.788696;-0.002942;,
    0.219950;0.794176;-0.004467;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.830629;,
     0.028511;0.807158;,
     0.011339;0.807158;,
     0.011338;0.830629;,
     0.032720;0.811166;,
     0.032720;0.826621;,
     0.007130;0.826621;,
     0.007130;0.811166;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_9 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;0.026195;,
    0.224267;0.780723;0.042054;,
    0.220121;0.791379;0.042054;,
    0.220121;0.791379;0.026195;,
    0.226031;0.778546;0.039385;,
    0.226031;0.778546;0.028863;,
    0.219950;0.794176;0.028863;,
    0.219950;0.794176;0.039385;,
    0.208846;0.770225;0.027339;,
    0.226031;0.778546;0.028863;,
    0.224267;0.780723;0.026195;,
    0.207879;0.772712;0.024670;,
    0.220121;0.791379;0.026195;,
    0.202628;0.786209;0.024670;,
    0.224267;0.780723;0.042054;,
    0.207879;0.772712;0.043578;,
    0.202628;0.786209;0.043578;,
    0.220121;0.791379;0.042054;,
    0.208846;0.770225;0.040910;,
    0.226031;0.778546;0.039385;,
    0.226031;0.778546;0.028863;,
    0.208846;0.770225;0.027339;,
    0.226031;0.778546;0.039385;,
    0.208846;0.770225;0.040910;,
    0.201660;0.788696;0.027339;,
    0.219950;0.794176;0.028863;,
    0.219950;0.794176;0.039385;,
    0.201660;0.788696;0.040910;,
    0.219950;0.794176;0.028863;,
    0.201660;0.788696;0.027339;,
    0.201660;0.788696;0.040910;,
    0.219950;0.794176;0.039385;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.777929;,
     0.028511;0.754458;,
     0.011339;0.754458;,
     0.011338;0.777929;,
     0.032720;0.758466;,
     0.032720;0.773921;,
     0.007130;0.773921;,
     0.007130;0.758466;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_0 {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.224267;0.780723;0.048121;,
    0.224267;0.780723;0.063980;,
    0.220121;0.791379;0.063980;,
    0.220121;0.791379;0.048121;,
    0.226031;0.778546;0.061311;,
    0.226031;0.778546;0.050789;,
    0.219950;0.794176;0.050789;,
    0.219950;0.794176;0.061311;,
    0.208846;0.770225;0.049265;,
    0.226031;0.778546;0.050789;,
    0.224267;0.780723;0.048121;,
    0.207879;0.772712;0.046596;,
    0.220121;0.791379;0.048121;,
    0.202628;0.786209;0.046596;,
    0.224267;0.780723;0.063980;,
    0.207879;0.772712;0.065504;,
    0.202628;0.786209;0.065504;,
    0.220121;0.791379;0.063980;,
    0.208846;0.770225;0.062835;,
    0.226031;0.778546;0.061311;,
    0.226031;0.778546;0.050789;,
    0.208846;0.770225;0.049265;,
    0.226031;0.778546;0.061311;,
    0.208846;0.770225;0.062835;,
    0.201660;0.788696;0.049265;,
    0.219950;0.794176;0.050789;,
    0.219950;0.794176;0.061311;,
    0.201660;0.788696;0.062835;,
    0.219950;0.794176;0.050789;,
    0.201660;0.788696;0.049265;,
    0.201660;0.788696;0.062835;,
    0.219950;0.794176;0.061311;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.028511;0.751516;,
     0.028511;0.728045;,
     0.011339;0.728045;,
     0.011338;0.751516;,
     0.032720;0.732053;,
     0.032720;0.747508;,
     0.007130;0.747508;,
     0.007130;0.732053;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_e {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.105361;,
    0.232258;0.760183;-0.089502;,
    0.228112;0.770838;-0.089502;,
    0.228112;0.770838;-0.105361;,
    0.234022;0.758006;-0.092171;,
    0.234022;0.758006;-0.102693;,
    0.227941;0.773635;-0.102693;,
    0.227941;0.773635;-0.092171;,
    0.216837;0.749684;-0.104217;,
    0.234022;0.758006;-0.102693;,
    0.232258;0.760183;-0.105361;,
    0.215870;0.752171;-0.106886;,
    0.228112;0.770838;-0.105361;,
    0.210619;0.765668;-0.106886;,
    0.232258;0.760183;-0.089502;,
    0.215870;0.752171;-0.087978;,
    0.210619;0.765668;-0.087978;,
    0.228112;0.770838;-0.089502;,
    0.216837;0.749684;-0.090646;,
    0.234022;0.758006;-0.092171;,
    0.234022;0.758006;-0.102693;,
    0.216837;0.749684;-0.104217;,
    0.234022;0.758006;-0.092171;,
    0.216837;0.749684;-0.090646;,
    0.209651;0.768155;-0.104217;,
    0.227941;0.773635;-0.102693;,
    0.227941;0.773635;-0.092171;,
    0.209651;0.768155;-0.090646;,
    0.227941;0.773635;-0.102693;,
    0.209651;0.768155;-0.104217;,
    0.209651;0.768155;-0.090646;,
    0.227941;0.773635;-0.092171;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.936898;,
     0.056243;0.913427;,
     0.039071;0.913427;,
     0.039070;0.936898;,
     0.060452;0.917435;,
     0.060452;0.932890;,
     0.034862;0.932890;,
     0.034862;0.917435;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_r {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.083435;,
    0.232258;0.760183;-0.067576;,
    0.228112;0.770838;-0.067576;,
    0.228112;0.770838;-0.083435;,
    0.234022;0.758006;-0.070245;,
    0.234022;0.758006;-0.080767;,
    0.227941;0.773635;-0.080767;,
    0.227941;0.773635;-0.070245;,
    0.216837;0.749684;-0.082291;,
    0.234022;0.758006;-0.080767;,
    0.232258;0.760183;-0.083435;,
    0.215870;0.752171;-0.084960;,
    0.228112;0.770838;-0.083435;,
    0.210619;0.765668;-0.084960;,
    0.232258;0.760183;-0.067576;,
    0.215870;0.752171;-0.066052;,
    0.210619;0.765668;-0.066052;,
    0.228112;0.770838;-0.067576;,
    0.216837;0.749684;-0.068720;,
    0.234022;0.758006;-0.070245;,
    0.234022;0.758006;-0.080767;,
    0.216837;0.749684;-0.082291;,
    0.234022;0.758006;-0.070245;,
    0.216837;0.749684;-0.068720;,
    0.209651;0.768155;-0.082291;,
    0.227941;0.773635;-0.080767;,
    0.227941;0.773635;-0.070245;,
    0.209651;0.768155;-0.068720;,
    0.227941;0.773635;-0.080767;,
    0.209651;0.768155;-0.082291;,
    0.209651;0.768155;-0.068720;,
    0.227941;0.773635;-0.070245;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.910021;,
     0.056243;0.886550;,
     0.039071;0.886550;,
     0.039070;0.910021;,
     0.060452;0.890558;,
     0.060452;0.906013;,
     0.034862;0.906013;,
     0.034862;0.890558;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_t {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.061509;,
    0.232258;0.760183;-0.045650;,
    0.228112;0.770838;-0.045650;,
    0.228112;0.770838;-0.061509;,
    0.234022;0.758006;-0.048319;,
    0.234022;0.758006;-0.058841;,
    0.227941;0.773635;-0.058841;,
    0.227941;0.773635;-0.048319;,
    0.216837;0.749684;-0.060365;,
    0.234022;0.758006;-0.058841;,
    0.232258;0.760183;-0.061509;,
    0.215870;0.752171;-0.063034;,
    0.228112;0.770838;-0.061509;,
    0.210619;0.765668;-0.063034;,
    0.232258;0.760183;-0.045650;,
    0.215870;0.752171;-0.044126;,
    0.210619;0.765668;-0.044126;,
    0.228112;0.770838;-0.045650;,
    0.216837;0.749684;-0.046794;,
    0.234022;0.758006;-0.048319;,
    0.234022;0.758006;-0.058841;,
    0.216837;0.749684;-0.060365;,
    0.234022;0.758006;-0.048319;,
    0.216837;0.749684;-0.046794;,
    0.209651;0.768155;-0.060365;,
    0.227941;0.773635;-0.058841;,
    0.227941;0.773635;-0.048319;,
    0.209651;0.768155;-0.046794;,
    0.227941;0.773635;-0.058841;,
    0.209651;0.768155;-0.060365;,
    0.209651;0.768155;-0.046794;,
    0.227941;0.773635;-0.048319;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.884007;,
     0.056243;0.860537;,
     0.039071;0.860537;,
     0.039070;0.884007;,
     0.060452;0.864545;,
     0.060452;0.879999;,
     0.034862;0.879999;,
     0.034862;0.864545;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_y {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.039583;,
    0.232258;0.760183;-0.023724;,
    0.228112;0.770838;-0.023724;,
    0.228112;0.770838;-0.039583;,
    0.234022;0.758006;-0.026393;,
    0.234022;0.758006;-0.036915;,
    0.227941;0.773635;-0.036915;,
    0.227941;0.773635;-0.026393;,
    0.216837;0.749684;-0.038439;,
    0.234022;0.758006;-0.036915;,
    0.232258;0.760183;-0.039583;,
    0.215870;0.752171;-0.041108;,
    0.228112;0.770838;-0.039583;,
    0.210619;0.765668;-0.041108;,
    0.232258;0.760183;-0.023724;,
    0.215870;0.752171;-0.022200;,
    0.210619;0.765668;-0.022200;,
    0.228112;0.770838;-0.023724;,
    0.216837;0.749684;-0.024868;,
    0.234022;0.758006;-0.026393;,
    0.234022;0.758006;-0.036915;,
    0.216837;0.749684;-0.038439;,
    0.234022;0.758006;-0.026393;,
    0.216837;0.749684;-0.024868;,
    0.209651;0.768155;-0.038439;,
    0.227941;0.773635;-0.036915;,
    0.227941;0.773635;-0.026393;,
    0.209651;0.768155;-0.024868;,
    0.227941;0.773635;-0.036915;,
    0.209651;0.768155;-0.038439;,
    0.209651;0.768155;-0.024868;,
    0.227941;0.773635;-0.026393;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.857353;,
     0.056243;0.833883;,
     0.039071;0.833883;,
     0.039070;0.857353;,
     0.060452;0.837891;,
     0.060452;0.853345;,
     0.034862;0.853345;,
     0.034862;0.837891;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_u {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;-0.017657;,
    0.232258;0.760183;-0.001798;,
    0.228112;0.770838;-0.001798;,
    0.228112;0.770838;-0.017657;,
    0.234022;0.758006;-0.004467;,
    0.234022;0.758006;-0.014989;,
    0.227941;0.773635;-0.014989;,
    0.227941;0.773635;-0.004467;,
    0.216837;0.749684;-0.016513;,
    0.234022;0.758006;-0.014989;,
    0.232258;0.760183;-0.017657;,
    0.215870;0.752171;-0.019182;,
    0.228112;0.770838;-0.017657;,
    0.210619;0.765668;-0.019182;,
    0.232258;0.760183;-0.001798;,
    0.215870;0.752171;-0.000274;,
    0.210619;0.765668;-0.000274;,
    0.228112;0.770838;-0.001798;,
    0.216837;0.749684;-0.002942;,
    0.234022;0.758006;-0.004467;,
    0.234022;0.758006;-0.014989;,
    0.216837;0.749684;-0.016513;,
    0.234022;0.758006;-0.004467;,
    0.216837;0.749684;-0.002942;,
    0.209651;0.768155;-0.016513;,
    0.227941;0.773635;-0.014989;,
    0.227941;0.773635;-0.004467;,
    0.209651;0.768155;-0.002942;,
    0.227941;0.773635;-0.014989;,
    0.209651;0.768155;-0.016513;,
    0.209651;0.768155;-0.002942;,
    0.227941;0.773635;-0.004467;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.831357;,
     0.056243;0.807887;,
     0.039071;0.807887;,
     0.039070;0.831357;,
     0.060452;0.811895;,
     0.060452;0.827349;,
     0.034862;0.827349;,
     0.034862;0.811895;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_o {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;0.026195;,
    0.232258;0.760183;0.042054;,
    0.228112;0.770838;0.042054;,
    0.228112;0.770838;0.026195;,
    0.234022;0.758006;0.039385;,
    0.234022;0.758006;0.028863;,
    0.227941;0.773635;0.028863;,
    0.227941;0.773635;0.039385;,
    0.216837;0.749684;0.027339;,
    0.234022;0.758006;0.028863;,
    0.232258;0.760183;0.026195;,
    0.215870;0.752171;0.024670;,
    0.228112;0.770838;0.026195;,
    0.210619;0.765668;0.024670;,
    0.232258;0.760183;0.042054;,
    0.215870;0.752171;0.043578;,
    0.210619;0.765668;0.043578;,
    0.228112;0.770838;0.042054;,
    0.216837;0.749684;0.040910;,
    0.234022;0.758006;0.039385;,
    0.234022;0.758006;0.028863;,
    0.216837;0.749684;0.027339;,
    0.234022;0.758006;0.039385;,
    0.216837;0.749684;0.040910;,
    0.209651;0.768155;0.027339;,
    0.227941;0.773635;0.028863;,
    0.227941;0.773635;0.039385;,
    0.209651;0.768155;0.040910;,
    0.227941;0.773635;0.028863;,
    0.209651;0.768155;0.027339;,
    0.209651;0.768155;0.040910;,
    0.227941;0.773635;0.039385;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.778658;,
     0.056243;0.755187;,
     0.039071;0.755187;,
     0.039070;0.778658;,
     0.060452;0.759195;,
     0.060452;0.774650;,
     0.034862;0.774650;,
     0.034862;0.759195;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_p {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.232258;0.760183;0.048121;,
    0.232258;0.760183;0.063980;,
    0.228112;0.770838;0.063980;,
    0.228112;0.770838;0.048121;,
    0.234022;0.758006;0.061311;,
    0.234022;0.758006;0.050789;,
    0.227941;0.773635;0.050789;,
    0.227941;0.773635;0.061311;,
    0.216837;0.749684;0.049265;,
    0.234022;0.758006;0.050789;,
    0.232258;0.760183;0.048121;,
    0.215870;0.752171;0.046596;,
    0.228112;0.770838;0.048121;,
    0.210619;0.765668;0.046596;,
    0.232258;0.760183;0.063980;,
    0.215870;0.752171;0.065504;,
    0.210619;0.765668;0.065504;,
    0.228112;0.770838;0.063980;,
    0.216837;0.749684;0.062835;,
    0.234022;0.758006;0.061311;,
    0.234022;0.758006;0.050789;,
    0.216837;0.749684;0.049265;,
    0.234022;0.758006;0.061311;,
    0.216837;0.749684;0.062835;,
    0.209651;0.768155;0.049265;,
    0.227941;0.773635;0.050789;,
    0.227941;0.773635;0.061311;,
    0.209651;0.768155;0.062835;,
    0.227941;0.773635;0.050789;,
    0.209651;0.768155;0.049265;,
    0.209651;0.768155;0.062835;,
    0.227941;0.773635;0.061311;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.056243;0.752245;,
     0.056243;0.728774;,
     0.039071;0.728774;,
     0.039070;0.752245;,
     0.060452;0.732782;,
     0.060452;0.748237;,
     0.034862;0.748237;,
     0.034862;0.732782;,
     0.197378;0.729317;,
     0.158133;0.730713;,
     0.159547;0.733947;,
     0.197761;0.732612;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.159547;0.743966;,
     0.197761;0.745301;,
     0.197761;0.732612;,
     0.159547;0.733947;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.198573;0.733041;,
     0.159321;0.734377;,
     0.159321;0.743596;,
     0.198573;0.744931;,
     0.158133;0.747200;,
     0.197378;0.748597;,
     0.197378;0.729317;,
     0.158133;0.730713;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_d {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.105361;,
    0.240249;0.739642;-0.089502;,
    0.236103;0.750298;-0.089502;,
    0.236103;0.750298;-0.105361;,
    0.242013;0.737465;-0.092171;,
    0.242013;0.737465;-0.102693;,
    0.235933;0.753095;-0.102693;,
    0.235933;0.753095;-0.092171;,
    0.224828;0.729144;-0.104217;,
    0.242013;0.737465;-0.102693;,
    0.240249;0.739642;-0.105361;,
    0.223861;0.731631;-0.106886;,
    0.236103;0.750298;-0.105361;,
    0.218610;0.745128;-0.106886;,
    0.240249;0.739642;-0.089502;,
    0.223861;0.731631;-0.087978;,
    0.218610;0.745128;-0.087978;,
    0.236103;0.750298;-0.089502;,
    0.224828;0.729144;-0.090646;,
    0.242013;0.737465;-0.092171;,
    0.242013;0.737465;-0.102693;,
    0.224828;0.729144;-0.104217;,
    0.242013;0.737465;-0.092171;,
    0.224828;0.729144;-0.090646;,
    0.217642;0.747615;-0.104217;,
    0.235933;0.753095;-0.102693;,
    0.235933;0.753095;-0.092171;,
    0.217642;0.747615;-0.090646;,
    0.235933;0.753095;-0.102693;,
    0.217642;0.747615;-0.104217;,
    0.217642;0.747615;-0.090646;,
    0.235933;0.753095;-0.092171;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.935790;,
     0.083202;0.912319;,
     0.066029;0.912319;,
     0.066029;0.935790;,
     0.087410;0.916327;,
     0.087410;0.931782;,
     0.061820;0.931782;,
     0.061820;0.916327;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_f {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.083435;,
    0.240249;0.739642;-0.067576;,
    0.236103;0.750298;-0.067576;,
    0.236103;0.750298;-0.083435;,
    0.242013;0.737465;-0.070245;,
    0.242013;0.737465;-0.080767;,
    0.235933;0.753095;-0.080767;,
    0.235933;0.753095;-0.070245;,
    0.224828;0.729144;-0.082291;,
    0.242013;0.737465;-0.080767;,
    0.240249;0.739642;-0.083435;,
    0.223861;0.731631;-0.084960;,
    0.236103;0.750298;-0.083435;,
    0.218610;0.745128;-0.084960;,
    0.240249;0.739642;-0.067576;,
    0.223861;0.731631;-0.066052;,
    0.218610;0.745128;-0.066052;,
    0.236103;0.750298;-0.067576;,
    0.224828;0.729144;-0.068720;,
    0.242013;0.737465;-0.070245;,
    0.242013;0.737465;-0.080767;,
    0.224828;0.729144;-0.082291;,
    0.242013;0.737465;-0.070245;,
    0.224828;0.729144;-0.068720;,
    0.217642;0.747615;-0.082291;,
    0.235933;0.753095;-0.080767;,
    0.235933;0.753095;-0.070245;,
    0.217642;0.747615;-0.068720;,
    0.235933;0.753095;-0.080767;,
    0.217642;0.747615;-0.082291;,
    0.217642;0.747615;-0.068720;,
    0.235933;0.753095;-0.070245;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.908913;,
     0.083202;0.885442;,
     0.066029;0.885442;,
     0.066029;0.908913;,
     0.087410;0.889450;,
     0.087410;0.904905;,
     0.061820;0.904905;,
     0.061820;0.889450;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_g {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.061509;,
    0.240249;0.739642;-0.045650;,
    0.236103;0.750298;-0.045650;,
    0.236103;0.750298;-0.061509;,
    0.242013;0.737465;-0.048319;,
    0.242013;0.737465;-0.058841;,
    0.235933;0.753095;-0.058841;,
    0.235933;0.753095;-0.048319;,
    0.224828;0.729144;-0.060365;,
    0.242013;0.737465;-0.058841;,
    0.240249;0.739642;-0.061509;,
    0.223861;0.731631;-0.063034;,
    0.236103;0.750298;-0.061509;,
    0.218610;0.745128;-0.063034;,
    0.240249;0.739642;-0.045650;,
    0.223861;0.731631;-0.044126;,
    0.218610;0.745128;-0.044126;,
    0.236103;0.750298;-0.045650;,
    0.224828;0.729144;-0.046794;,
    0.242013;0.737465;-0.048319;,
    0.242013;0.737465;-0.058841;,
    0.224828;0.729144;-0.060365;,
    0.242013;0.737465;-0.048319;,
    0.224828;0.729144;-0.046794;,
    0.217642;0.747615;-0.060365;,
    0.235933;0.753095;-0.058841;,
    0.235933;0.753095;-0.048319;,
    0.217642;0.747615;-0.046794;,
    0.235933;0.753095;-0.058841;,
    0.217642;0.747615;-0.060365;,
    0.217642;0.747615;-0.046794;,
    0.235933;0.753095;-0.048319;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.882900;,
     0.083202;0.859429;,
     0.066029;0.859429;,
     0.066029;0.882900;,
     0.087410;0.863437;,
     0.087410;0.878892;,
     0.061820;0.878892;,
     0.061820;0.863437;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_h {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.039583;,
    0.240249;0.739642;-0.023724;,
    0.236103;0.750298;-0.023724;,
    0.236103;0.750298;-0.039583;,
    0.242013;0.737465;-0.026393;,
    0.242013;0.737465;-0.036915;,
    0.235933;0.753095;-0.036915;,
    0.235933;0.753095;-0.026393;,
    0.224828;0.729144;-0.038439;,
    0.242013;0.737465;-0.036915;,
    0.240249;0.739642;-0.039583;,
    0.223861;0.731631;-0.041108;,
    0.236103;0.750298;-0.039583;,
    0.218610;0.745128;-0.041108;,
    0.240249;0.739642;-0.023724;,
    0.223861;0.731631;-0.022200;,
    0.218610;0.745128;-0.022200;,
    0.236103;0.750298;-0.023724;,
    0.224828;0.729144;-0.024868;,
    0.242013;0.737465;-0.026393;,
    0.242013;0.737465;-0.036915;,
    0.224828;0.729144;-0.038439;,
    0.242013;0.737465;-0.026393;,
    0.224828;0.729144;-0.024868;,
    0.217642;0.747615;-0.038439;,
    0.235933;0.753095;-0.036915;,
    0.235933;0.753095;-0.026393;,
    0.217642;0.747615;-0.024868;,
    0.235933;0.753095;-0.036915;,
    0.217642;0.747615;-0.038439;,
    0.217642;0.747615;-0.024868;,
    0.235933;0.753095;-0.026393;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.856246;,
     0.083202;0.832775;,
     0.066029;0.832775;,
     0.066029;0.856246;,
     0.087410;0.836783;,
     0.087410;0.852238;,
     0.061820;0.852238;,
     0.061820;0.836783;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_j {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;-0.017657;,
    0.240249;0.739642;-0.001798;,
    0.236103;0.750298;-0.001798;,
    0.236103;0.750298;-0.017657;,
    0.242013;0.737465;-0.004467;,
    0.242013;0.737465;-0.014989;,
    0.235933;0.753095;-0.014989;,
    0.235933;0.753095;-0.004467;,
    0.224828;0.729144;-0.016513;,
    0.242013;0.737465;-0.014989;,
    0.240249;0.739642;-0.017657;,
    0.223861;0.731631;-0.019182;,
    0.236103;0.750298;-0.017657;,
    0.218610;0.745128;-0.019182;,
    0.240249;0.739642;-0.001798;,
    0.223861;0.731631;-0.000274;,
    0.218610;0.745128;-0.000274;,
    0.236103;0.750298;-0.001798;,
    0.224828;0.729144;-0.002942;,
    0.242013;0.737465;-0.004467;,
    0.242013;0.737465;-0.014989;,
    0.224828;0.729144;-0.016513;,
    0.242013;0.737465;-0.004467;,
    0.224828;0.729144;-0.002942;,
    0.217642;0.747615;-0.016513;,
    0.235933;0.753095;-0.014989;,
    0.235933;0.753095;-0.004467;,
    0.217642;0.747615;-0.002942;,
    0.235933;0.753095;-0.014989;,
    0.217642;0.747615;-0.016513;,
    0.217642;0.747615;-0.002942;,
    0.235933;0.753095;-0.004467;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.830250;,
     0.083202;0.806779;,
     0.066029;0.806779;,
     0.066029;0.830250;,
     0.087410;0.810787;,
     0.087410;0.826242;,
     0.061820;0.826242;,
     0.061820;0.810787;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_l {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;0.026195;,
    0.240249;0.739642;0.042054;,
    0.236103;0.750298;0.042054;,
    0.236103;0.750298;0.026195;,
    0.242013;0.737465;0.039385;,
    0.242013;0.737465;0.028863;,
    0.235933;0.753095;0.028863;,
    0.235933;0.753095;0.039385;,
    0.224828;0.729144;0.027339;,
    0.242013;0.737465;0.028863;,
    0.240249;0.739642;0.026195;,
    0.223861;0.731631;0.024670;,
    0.236103;0.750298;0.026195;,
    0.218610;0.745128;0.024670;,
    0.240249;0.739642;0.042054;,
    0.223861;0.731631;0.043578;,
    0.218610;0.745128;0.043578;,
    0.236103;0.750298;0.042054;,
    0.224828;0.729144;0.040910;,
    0.242013;0.737465;0.039385;,
    0.242013;0.737465;0.028863;,
    0.224828;0.729144;0.027339;,
    0.242013;0.737465;0.039385;,
    0.224828;0.729144;0.040910;,
    0.217642;0.747615;0.027339;,
    0.235933;0.753095;0.028863;,
    0.235933;0.753095;0.039385;,
    0.217642;0.747615;0.040910;,
    0.235933;0.753095;0.028863;,
    0.217642;0.747615;0.027339;,
    0.217642;0.747615;0.040910;,
    0.235933;0.753095;0.039385;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.777550;,
     0.083202;0.754080;,
     0.066029;0.754080;,
     0.066029;0.777550;,
     0.087410;0.758088;,
     0.087410;0.773542;,
     0.061820;0.773542;,
     0.061820;0.758088;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_enter {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240249;0.739642;0.048121;,
    0.240249;0.739642;0.063980;,
    0.236103;0.750298;0.063980;,
    0.236103;0.750298;0.048121;,
    0.242013;0.737465;0.061311;,
    0.242013;0.737465;0.050789;,
    0.235933;0.753095;0.050789;,
    0.235933;0.753095;0.061311;,
    0.224828;0.729144;0.049265;,
    0.242013;0.737465;0.050789;,
    0.240249;0.739642;0.048121;,
    0.223861;0.731631;0.046596;,
    0.236103;0.750298;0.048121;,
    0.218610;0.745128;0.046596;,
    0.240249;0.739642;0.063980;,
    0.223861;0.731631;0.065504;,
    0.218610;0.745128;0.065504;,
    0.236103;0.750298;0.063980;,
    0.224828;0.729144;0.062835;,
    0.242013;0.737465;0.061311;,
    0.242013;0.737465;0.050789;,
    0.224828;0.729144;0.049265;,
    0.242013;0.737465;0.061311;,
    0.224828;0.729144;0.062835;,
    0.217642;0.747615;0.049265;,
    0.235933;0.753095;0.050789;,
    0.235933;0.753095;0.061311;,
    0.217642;0.747615;0.062835;,
    0.235933;0.753095;0.050789;,
    0.217642;0.747615;0.049265;,
    0.217642;0.747615;0.062835;,
    0.235933;0.753095;0.061311;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.083201;0.751137;,
     0.083202;0.727666;,
     0.066029;0.727666;,
     0.066029;0.751137;,
     0.087410;0.731674;,
     0.087410;0.747129;,
     0.061820;0.747129;,
     0.061820;0.731674;,
     0.197419;0.729290;,
     0.158174;0.730687;,
     0.159589;0.733921;,
     0.197802;0.732586;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.159588;0.743939;,
     0.197802;0.745275;,
     0.197802;0.732586;,
     0.159589;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158174;0.747174;,
     0.197419;0.748570;,
     0.197419;0.729290;,
     0.158174;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_c {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.105361;,
    0.248240;0.719102;-0.089502;,
    0.244095;0.729758;-0.089502;,
    0.244095;0.729758;-0.105361;,
    0.250004;0.716925;-0.092171;,
    0.250004;0.716925;-0.102693;,
    0.243924;0.732555;-0.102693;,
    0.243924;0.732555;-0.092171;,
    0.232819;0.708604;-0.104217;,
    0.250004;0.716925;-0.102693;,
    0.248240;0.719102;-0.105361;,
    0.231852;0.711091;-0.106886;,
    0.244095;0.729758;-0.105361;,
    0.226601;0.724588;-0.106886;,
    0.248240;0.719102;-0.089502;,
    0.231852;0.711091;-0.087978;,
    0.226601;0.724588;-0.087978;,
    0.244095;0.729758;-0.089502;,
    0.232819;0.708604;-0.090646;,
    0.250004;0.716925;-0.092171;,
    0.250004;0.716925;-0.102693;,
    0.232819;0.708604;-0.104217;,
    0.250004;0.716925;-0.092171;,
    0.232819;0.708604;-0.090646;,
    0.225633;0.727075;-0.104217;,
    0.243924;0.732555;-0.102693;,
    0.243924;0.732555;-0.092171;,
    0.225633;0.727075;-0.090646;,
    0.243924;0.732555;-0.102693;,
    0.225633;0.727075;-0.104217;,
    0.225633;0.727075;-0.090646;,
    0.243924;0.732555;-0.092171;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.936025;,
     0.110939;0.912554;,
     0.093767;0.912554;,
     0.093766;0.936025;,
     0.115148;0.916562;,
     0.115148;0.932017;,
     0.089558;0.932017;,
     0.089558;0.916562;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_v {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.083435;,
    0.248240;0.719102;-0.067576;,
    0.244095;0.729758;-0.067576;,
    0.244095;0.729758;-0.083435;,
    0.250004;0.716925;-0.070245;,
    0.250004;0.716925;-0.080767;,
    0.243924;0.732555;-0.080767;,
    0.243924;0.732555;-0.070245;,
    0.232819;0.708604;-0.082291;,
    0.250004;0.716925;-0.080767;,
    0.248240;0.719102;-0.083435;,
    0.231852;0.711091;-0.084960;,
    0.244095;0.729758;-0.083435;,
    0.226601;0.724588;-0.084960;,
    0.248240;0.719102;-0.067576;,
    0.231852;0.711091;-0.066052;,
    0.226601;0.724588;-0.066052;,
    0.244095;0.729758;-0.067576;,
    0.232819;0.708604;-0.068720;,
    0.250004;0.716925;-0.070245;,
    0.250004;0.716925;-0.080767;,
    0.232819;0.708604;-0.082291;,
    0.250004;0.716925;-0.070245;,
    0.232819;0.708604;-0.068720;,
    0.225633;0.727075;-0.082291;,
    0.243924;0.732555;-0.080767;,
    0.243924;0.732555;-0.070245;,
    0.225633;0.727075;-0.068720;,
    0.243924;0.732555;-0.080767;,
    0.225633;0.727075;-0.082291;,
    0.225633;0.727075;-0.068720;,
    0.243924;0.732555;-0.070245;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;0.000000;,
     0.864803;0.502112;0.000000;,
     0.976759;0.214340;0.000000;,
     0.976759;0.214340;0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;0.000000;,
     0.998140;0.060966;0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.909148;,
     0.110939;0.885677;,
     0.093767;0.885677;,
     0.093766;0.909148;,
     0.115148;0.889685;,
     0.115148;0.905140;,
     0.089558;0.905140;,
     0.089558;0.889685;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_b {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.061509;,
    0.248240;0.719102;-0.045650;,
    0.244095;0.729758;-0.045650;,
    0.244095;0.729758;-0.061509;,
    0.250004;0.716925;-0.048319;,
    0.250004;0.716925;-0.058841;,
    0.243924;0.732555;-0.058841;,
    0.243924;0.732555;-0.048319;,
    0.232819;0.708604;-0.060365;,
    0.250004;0.716925;-0.058841;,
    0.248240;0.719102;-0.061509;,
    0.231852;0.711091;-0.063034;,
    0.244095;0.729758;-0.061509;,
    0.226601;0.724588;-0.063034;,
    0.248240;0.719102;-0.045650;,
    0.231852;0.711091;-0.044126;,
    0.226601;0.724588;-0.044126;,
    0.244095;0.729758;-0.045650;,
    0.232819;0.708604;-0.046794;,
    0.250004;0.716925;-0.048319;,
    0.250004;0.716925;-0.058841;,
    0.232819;0.708604;-0.060365;,
    0.250004;0.716925;-0.048319;,
    0.232819;0.708604;-0.046794;,
    0.225633;0.727075;-0.060365;,
    0.243924;0.732555;-0.058841;,
    0.243924;0.732555;-0.048319;,
    0.225633;0.727075;-0.046794;,
    0.243924;0.732555;-0.058841;,
    0.225633;0.727075;-0.060365;,
    0.225633;0.727075;-0.046794;,
    0.243924;0.732555;-0.048319;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.883134;,
     0.110939;0.859664;,
     0.093767;0.859664;,
     0.093766;0.883134;,
     0.115148;0.863672;,
     0.115148;0.879126;,
     0.089558;0.879126;,
     0.089558;0.863672;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_n {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.039583;,
    0.248240;0.719102;-0.023724;,
    0.244095;0.729758;-0.023724;,
    0.244095;0.729758;-0.039583;,
    0.250004;0.716925;-0.026393;,
    0.250004;0.716925;-0.036915;,
    0.243924;0.732555;-0.036915;,
    0.243924;0.732555;-0.026393;,
    0.232819;0.708604;-0.038439;,
    0.250004;0.716925;-0.036915;,
    0.248240;0.719102;-0.039583;,
    0.231852;0.711091;-0.041108;,
    0.244095;0.729758;-0.039583;,
    0.226601;0.724588;-0.041108;,
    0.248240;0.719102;-0.023724;,
    0.231852;0.711091;-0.022200;,
    0.226601;0.724588;-0.022200;,
    0.244095;0.729758;-0.023724;,
    0.232819;0.708604;-0.024868;,
    0.250004;0.716925;-0.026393;,
    0.250004;0.716925;-0.036915;,
    0.232819;0.708604;-0.038439;,
    0.250004;0.716925;-0.026393;,
    0.232819;0.708604;-0.024868;,
    0.225633;0.727075;-0.038439;,
    0.243924;0.732555;-0.036915;,
    0.243924;0.732555;-0.026393;,
    0.225633;0.727075;-0.024868;,
    0.243924;0.732555;-0.036915;,
    0.225633;0.727075;-0.038439;,
    0.225633;0.727075;-0.024868;,
    0.243924;0.732555;-0.026393;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.856481;,
     0.110939;0.833010;,
     0.093767;0.833010;,
     0.093766;0.856481;,
     0.115148;0.837018;,
     0.115148;0.852473;,
     0.089558;0.852473;,
     0.089558;0.837018;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_m {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;-0.017657;,
    0.248240;0.719102;-0.001798;,
    0.244095;0.729758;-0.001798;,
    0.244095;0.729758;-0.017657;,
    0.250004;0.716925;-0.004467;,
    0.250004;0.716925;-0.014989;,
    0.243924;0.732555;-0.014989;,
    0.243924;0.732555;-0.004467;,
    0.232819;0.708604;-0.016513;,
    0.250004;0.716925;-0.014989;,
    0.248240;0.719102;-0.017657;,
    0.231852;0.711091;-0.019182;,
    0.244095;0.729758;-0.017657;,
    0.226601;0.724588;-0.019182;,
    0.248240;0.719102;-0.001798;,
    0.231852;0.711091;-0.000274;,
    0.226601;0.724588;-0.000274;,
    0.244095;0.729758;-0.001798;,
    0.232819;0.708604;-0.002942;,
    0.250004;0.716925;-0.004467;,
    0.250004;0.716925;-0.014989;,
    0.232819;0.708604;-0.016513;,
    0.250004;0.716925;-0.004467;,
    0.232819;0.708604;-0.002942;,
    0.225633;0.727075;-0.016513;,
    0.243924;0.732555;-0.014989;,
    0.243924;0.732555;-0.004467;,
    0.225633;0.727075;-0.002942;,
    0.243924;0.732555;-0.014989;,
    0.225633;0.727075;-0.016513;,
    0.225633;0.727075;-0.002942;,
    0.243924;0.732555;-0.004467;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.830484;,
     0.110939;0.807014;,
     0.093767;0.807014;,
     0.093766;0.830484;,
     0.115148;0.811022;,
     0.115148;0.826476;,
     0.089558;0.826476;,
     0.089558;0.811022;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_unassigned {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;0.026195;,
    0.248240;0.719102;0.042054;,
    0.244095;0.729758;0.042054;,
    0.244095;0.729758;0.026195;,
    0.250004;0.716925;0.039385;,
    0.250004;0.716925;0.028863;,
    0.243924;0.732555;0.028863;,
    0.243924;0.732555;0.039385;,
    0.232819;0.708604;0.027339;,
    0.250004;0.716925;0.028863;,
    0.248240;0.719102;0.026195;,
    0.231852;0.711091;0.024670;,
    0.244095;0.729758;0.026195;,
    0.226601;0.724588;0.024670;,
    0.248240;0.719102;0.042054;,
    0.231852;0.711091;0.043578;,
    0.226601;0.724588;0.043578;,
    0.244095;0.729758;0.042054;,
    0.232819;0.708604;0.040910;,
    0.250004;0.716925;0.039385;,
    0.250004;0.716925;0.028863;,
    0.232819;0.708604;0.027339;,
    0.250004;0.716925;0.039385;,
    0.232819;0.708604;0.040910;,
    0.225633;0.727075;0.027339;,
    0.243924;0.732555;0.028863;,
    0.243924;0.732555;0.039385;,
    0.225633;0.727075;0.040910;,
    0.243924;0.732555;0.028863;,
    0.225633;0.727075;0.027339;,
    0.225633;0.727075;0.040910;,
    0.243924;0.732555;0.039385;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;-0.000000;,
     0.864803;0.502112;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.976759;0.214340;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.998140;0.060966;-0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.777785;,
     0.110939;0.754314;,
     0.093767;0.754314;,
     0.093766;0.777785;,
     0.115148;0.758322;,
     0.115148;0.773777;,
     0.089558;0.773777;,
     0.089558;0.758322;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_backspace {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.248240;0.719102;0.048121;,
    0.248240;0.719102;0.063980;,
    0.244095;0.729758;0.063980;,
    0.244095;0.729758;0.048121;,
    0.250004;0.716925;0.061311;,
    0.250004;0.716925;0.050789;,
    0.243924;0.732555;0.050789;,
    0.243924;0.732555;0.061311;,
    0.232819;0.708604;0.049265;,
    0.250004;0.716925;0.050789;,
    0.248240;0.719102;0.048121;,
    0.231852;0.711091;0.046596;,
    0.244095;0.729758;0.048121;,
    0.226601;0.724588;0.046596;,
    0.248240;0.719102;0.063980;,
    0.231852;0.711091;0.065504;,
    0.226601;0.724588;0.065504;,
    0.244095;0.729758;0.063980;,
    0.232819;0.708604;0.062835;,
    0.250004;0.716925;0.061311;,
    0.250004;0.716925;0.050789;,
    0.232819;0.708604;0.049265;,
    0.250004;0.716925;0.061311;,
    0.232819;0.708604;0.062835;,
    0.225633;0.727075;0.049265;,
    0.243924;0.732555;0.050789;,
    0.243924;0.732555;0.061311;,
    0.225633;0.727075;0.062835;,
    0.243924;0.732555;0.050789;,
    0.225633;0.727075;0.049265;,
    0.225633;0.727075;0.062835;,
    0.243924;0.732555;0.061311;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;4,0,1;,
    3;4,5,0;,
    3;6,2,3;,
    3;6,7,2;,
    3;8,10,9;,
    3;8,11,10;,
    3;11,12,10;,
    3;11,13,12;,
    3;14,16,15;,
    3;14,17,16;,
    3;18,20,19;,
    3;18,21,20;,
    3;15,22,14;,
    3;15,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,13,29;,
    3;28,12,13;,
    3;17,30,16;,
    3;17,31,30;;

    MeshNormals {
     32;
     0.864803;0.502112;0.000000;,
     0.864803;0.502112;0.000000;,
     0.976759;0.214340;0.000000;,
     0.976759;0.214340;0.000000;,
     0.776911;0.629610;-0.000000;,
     0.776911;0.629610;-0.000000;,
     0.998140;0.060966;0.000000;,
     0.998140;0.060966;0.000000;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.235402;-0.309567;-0.921278;,
     0.235402;-0.309567;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     -0.035697;0.387262;-0.921278;,
     0.235402;-0.309567;0.921278;,
     0.235402;-0.309567;0.921278;,
     -0.035697;0.387262;0.921278;,
     -0.035697;0.387262;0.921278;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;-0.385888;,
     0.430800;-0.815783;0.385888;,
     0.430800;-0.815783;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;-0.385888;,
     -0.233775;0.892435;0.385888;,
     -0.233775;0.892435;0.385888;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;4,0,1;,
     3;4,5,0;,
     3;6,2,3;,
     3;6,7,2;,
     3;8,10,9;,
     3;8,11,10;,
     3;11,12,10;,
     3;11,13,12;,
     3;14,16,15;,
     3;14,17,16;,
     3;18,20,19;,
     3;18,21,20;,
     3;15,22,14;,
     3;15,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,13,29;,
     3;28,12,13;,
     3;17,30,16;,
     3;17,31,30;;
    }

    MeshTextureCoords {
     32;
     0.110939;0.751372;,
     0.110939;0.727901;,
     0.093767;0.727901;,
     0.093766;0.751372;,
     0.115148;0.731909;,
     0.115148;0.747364;,
     0.089558;0.747364;,
     0.089558;0.731909;,
     0.197378;0.729290;,
     0.158133;0.730687;,
     0.159547;0.733921;,
     0.197761;0.732586;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.159547;0.743939;,
     0.197761;0.745275;,
     0.197761;0.732586;,
     0.159547;0.733921;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.198573;0.733017;,
     0.159321;0.734352;,
     0.159321;0.743572;,
     0.198573;0.744907;,
     0.158133;0.747174;,
     0.197378;0.748570;,
     0.197378;0.729290;,
     0.158133;0.730687;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame 294_space {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    32;
    0.240811;0.688063;-0.148069;,
    0.255898;0.695569;-0.146545;,
    0.256511;0.698671;-0.149213;,
    0.239843;0.690550;-0.150738;,
    0.252365;0.709326;-0.149213;,
    0.234592;0.704047;-0.150738;,
    0.256511;0.698671;-0.149213;,
    0.256511;0.698671;0.063980;,
    0.252365;0.709326;0.063980;,
    0.252365;0.709326;-0.149213;,
    0.255898;0.695569;0.061311;,
    0.255898;0.695569;-0.146545;,
    0.256511;0.698671;0.063980;,
    0.239843;0.690550;0.065504;,
    0.234592;0.704047;0.065504;,
    0.252365;0.709326;0.063980;,
    0.240811;0.688063;0.062836;,
    0.255898;0.695569;0.061311;,
    0.255898;0.695569;-0.146545;,
    0.240811;0.688063;-0.148069;,
    0.255898;0.695569;0.061311;,
    0.240811;0.688063;0.062836;,
    0.233625;0.706534;-0.148069;,
    0.249817;0.711198;-0.146545;,
    0.249817;0.711198;0.061311;,
    0.233625;0.706534;0.062836;,
    0.249817;0.711198;-0.146545;,
    0.233625;0.706534;-0.148069;,
    0.249817;0.711198;-0.146545;,
    0.249817;0.711198;0.061311;,
    0.233625;0.706534;0.062836;,
    0.249817;0.711198;0.061311;;
    22;
    3;0,2,1;,
    3;0,3,2;,
    3;3,4,2;,
    3;3,5,4;,
    3;6,8,7;,
    3;6,9,8;,
    3;10,6,7;,
    3;10,11,6;,
    3;12,14,13;,
    3;12,15,14;,
    3;16,18,17;,
    3;16,19,18;,
    3;13,20,12;,
    3;13,21,20;,
    3;22,24,23;,
    3;22,25,24;,
    3;26,5,27;,
    3;26,4,5;,
    3;28,8,9;,
    3;28,29,8;,
    3;15,30,14;,
    3;15,31,30;;

    MeshNormals {
     32;
     0.440884;-0.819129;-0.366945;,
     0.697977;-0.665498;-0.264462;,
     0.245347;-0.325293;-0.913230;,
     0.245347;-0.325293;-0.913230;,
     -0.038994;0.405573;-0.913230;,
     -0.038994;0.405573;-0.913230;,
     0.996134;0.087846;0.000000;,
     0.996134;0.087846;0.000000;,
     0.793599;0.608442;-0.000000;,
     0.793599;0.608442;-0.000000;,
     0.697977;-0.665498;0.264462;,
     0.697977;-0.665498;-0.264462;,
     0.245347;-0.325293;0.913230;,
     0.245347;-0.325293;0.913230;,
     -0.038994;0.405573;0.913230;,
     -0.038994;0.405573;0.913230;,
     0.440884;-0.819129;0.366945;,
     0.697977;-0.665498;0.264462;,
     0.697977;-0.665498;-0.264462;,
     0.440884;-0.819129;-0.366945;,
     0.697977;-0.665498;0.264462;,
     0.440884;-0.819129;0.366945;,
     -0.228603;0.901716;-0.366945;,
     0.064719;0.962222;-0.264462;,
     0.064719;0.962222;0.264462;,
     -0.228603;0.901716;0.366945;,
     0.064719;0.962222;-0.264462;,
     -0.228603;0.901716;-0.366945;,
     0.064719;0.962222;-0.264462;,
     0.064719;0.962222;0.264462;,
     -0.228603;0.901716;0.366945;,
     0.064719;0.962222;0.264462;;
     22;
     3;0,2,1;,
     3;0,3,2;,
     3;3,4,2;,
     3;3,5,4;,
     3;6,8,7;,
     3;6,9,8;,
     3;10,6,7;,
     3;10,11,6;,
     3;12,14,13;,
     3;12,15,14;,
     3;16,18,17;,
     3;16,19,18;,
     3;13,20,12;,
     3;13,21,20;,
     3;22,24,23;,
     3;22,25,24;,
     3;26,5,27;,
     3;26,4,5;,
     3;28,8,9;,
     3;28,29,8;,
     3;15,30,14;,
     3;15,31,30;;
    }

    MeshTextureCoords {
     32;
     0.174637;0.728853;,
     0.157912;0.730250;,
     0.158929;0.733484;,
     0.174912;0.732149;,
     0.158929;0.743502;,
     0.174912;0.744838;,
     0.145745;0.988751;,
     0.145745;0.727871;,
     0.128573;0.727871;,
     0.128572;0.988751;,
     0.149954;0.732396;,
     0.149953;0.984226;,
     0.158929;0.743502;,
     0.174912;0.744838;,
     0.174912;0.732149;,
     0.158929;0.733484;,
     0.175705;0.732577;,
     0.158975;0.733913;,
     0.158975;0.743132;,
     0.175705;0.744468;,
     0.157912;0.746736;,
     0.174637;0.748133;,
     0.175705;0.732577;,
     0.158975;0.733913;,
     0.158975;0.743132;,
     0.175705;0.744468;,
     0.157912;0.746736;,
     0.174637;0.748133;,
     0.124364;0.984226;,
     0.124364;0.732396;,
     0.174637;0.728853;,
     0.157912;0.730250;;
    }

    VertexDuplicationIndices {
     32;
     32;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31;
    }

    MeshMaterialList {
     1;
     22;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material 294 {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.020980;0.020980;0.020980;;
      TextureFilename {
       "294/294.png";
      }
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    16;
    0.260837;1.076985;0.105247;,
    0.259216;1.068924;0.125098;,
    0.256717;1.056505;0.137766;,
    0.252802;1.037044;0.145988;,
    0.215528;0.851772;0.145988;,
    0.211613;0.832311;0.137766;,
    0.209114;0.819892;0.125098;,
    0.207493;0.811831;0.105247;,
    0.207493;0.811831;-0.190480;,
    0.209114;0.819892;-0.210331;,
    0.211613;0.832311;-0.222999;,
    0.215528;0.851772;-0.231222;,
    0.252802;1.037044;-0.231222;,
    0.256717;1.056505;-0.222999;,
    0.259216;1.068924;-0.210331;,
    0.260837;1.076985;-0.190480;;
    14;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;,
    3;0,8,7;,
    3;0,9,8;,
    3;0,10,9;,
    3;0,11,10;,
    3;0,12,11;,
    3;0,13,12;,
    3;0,14,13;,
    3;0,15,14;;

    MeshNormals {
     16;
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;,
     -0.980357;0.197232;-0.000000;;
     14;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;,
     3;0,8,7;,
     3;0,9,8;,
     3;0,10,9;,
     3;0,11,10;,
     3;0,12,11;,
     3;0,13,12;,
     3;0,14,13;,
     3;0,15,14;;
    }

    MeshTextureCoords {
     16;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     16;
     16;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15;
    }

    MeshMaterialList {
     1;
     14;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    181;
    0.263173;0.678122;0.146743;,
    0.259557;0.687417;0.146743;,
    0.263224;0.683493;0.146743;,
    0.215134;0.801603;0.146743;,
    0.211518;0.810898;0.146743;,
    0.215184;0.806974;0.146743;,
    0.215184;0.806974;-0.231977;,
    0.211518;0.810898;-0.231977;,
    0.215134;0.801603;-0.231977;,
    0.263224;0.683493;-0.231977;,
    0.259557;0.687417;-0.231977;,
    0.263173;0.678122;-0.231977;,
    -0.274837;0.069057;0.297312;,
    0.260837;0.069057;0.297312;,
    0.260837;0.069057;-0.297312;,
    -0.274837;0.069057;-0.297312;,
    -0.274837;0.060076;0.310312;,
    0.274837;0.060076;0.310312;,
    0.274837;0.060076;0.297312;,
    -0.274837;0.060076;0.297312;,
    0.274837;0.060076;-0.310312;,
    -0.274837;0.060076;-0.310312;,
    -0.274837;0.060076;-0.297312;,
    0.274837;0.060076;-0.297312;,
    0.207493;0.811831;-0.231222;,
    0.244455;0.674714;-0.231222;,
    0.244455;0.674714;0.145988;,
    0.207493;0.811831;0.145988;,
    0.260837;0.845918;0.266181;,
    0.206608;0.845918;0.266181;,
    0.206608;0.821035;0.266181;,
    0.260837;0.821035;0.266181;,
    0.260837;0.821035;0.258058;,
    0.206608;0.821035;0.258058;,
    0.206608;0.845918;0.258058;,
    0.260837;0.845918;0.258058;,
    0.260837;0.524061;-0.093289;,
    0.020315;0.524061;-0.093289;,
    0.020315;0.388964;-0.093289;,
    0.260837;0.388964;-0.093289;,
    0.246650;0.388964;-0.214373;,
    0.020315;0.388964;-0.214373;,
    0.020315;0.524061;-0.214373;,
    0.246650;0.524061;-0.214373;,
    0.227399;0.074403;-0.250945;,
    0.222729;0.074403;-0.239671;,
    0.211455;0.074403;-0.235001;,
    0.200181;0.074403;-0.239671;,
    0.195512;0.074403;-0.250945;,
    0.200181;0.074403;-0.262219;,
    0.211455;0.074403;-0.266889;,
    0.222729;0.074403;-0.262219;,
    0.219798;0.000000;-0.259288;,
    0.211455;0.000000;-0.262743;,
    0.203112;0.000000;-0.259288;,
    0.199657;0.000000;-0.250945;,
    0.203112;0.000000;-0.242602;,
    0.211455;0.000000;-0.239147;,
    0.219798;0.000000;-0.242602;,
    0.223253;0.000000;-0.250945;,
    -0.209513;0.074403;-0.250945;,
    -0.214182;0.074403;-0.239671;,
    -0.225456;0.074403;-0.235001;,
    -0.236730;0.074403;-0.239671;,
    -0.241400;0.074403;-0.250945;,
    -0.236730;0.074403;-0.262219;,
    -0.225456;0.074403;-0.266889;,
    -0.214182;0.074403;-0.262219;,
    -0.217114;0.000000;-0.259288;,
    -0.225456;0.000000;-0.262743;,
    -0.233799;0.000000;-0.259288;,
    -0.237255;0.000000;-0.250945;,
    -0.233799;0.000000;-0.242602;,
    -0.225456;0.000000;-0.239147;,
    -0.217114;0.000000;-0.242602;,
    -0.213658;0.000000;-0.250945;,
    -0.209512;0.074403;0.250945;,
    -0.214182;0.074403;0.262219;,
    -0.225456;0.074403;0.266889;,
    -0.236730;0.074403;0.262219;,
    -0.241399;0.074403;0.250945;,
    -0.236730;0.074403;0.239671;,
    -0.225456;0.074403;0.235001;,
    -0.214182;0.074403;0.239671;,
    -0.217113;0.000000;0.242602;,
    -0.225456;0.000000;0.239147;,
    -0.233798;0.000000;0.242602;,
    -0.237254;0.000000;0.250945;,
    -0.233798;0.000000;0.259288;,
    -0.225456;0.000000;0.262743;,
    -0.217113;0.000000;0.259288;,
    -0.213658;0.000000;0.250945;,
    0.227252;0.074403;0.250945;,
    0.222582;0.074403;0.262219;,
    0.211308;0.074403;0.266889;,
    0.200035;0.074403;0.262219;,
    0.195365;0.074403;0.250945;,
    0.200035;0.074403;0.239671;,
    0.211308;0.074403;0.235001;,
    0.222582;0.074403;0.239671;,
    0.219651;0.000000;0.242602;,
    0.211308;0.000000;0.239147;,
    0.202966;0.000000;0.242602;,
    0.199510;0.000000;0.250945;,
    0.202966;0.000000;0.259288;,
    0.211308;0.000000;0.262743;,
    0.219651;0.000000;0.259288;,
    0.223107;0.000000;0.250945;,
    0.260837;0.388964;-0.105485;,
    0.260837;0.524061;-0.105485;,
    0.239523;0.674276;0.146743;,
    0.235907;0.683570;0.146743;,
    0.187867;0.807051;-0.231977;,
    0.191483;0.797757;-0.231977;,
    0.187867;0.807051;0.146743;,
    0.235907;0.683570;-0.231977;,
    0.239523;0.674276;-0.231977;,
    0.191483;0.797757;0.146743;,
    -0.274837;1.124066;-0.310312;,
    -0.274837;1.124066;0.310312;,
    0.241119;0.507290;-0.124581;,
    0.232253;0.604846;-0.130808;,
    0.199746;0.604846;-0.130808;,
    0.182449;0.507290;-0.124581;,
    0.241119;0.507290;-0.184207;,
    0.232253;0.604846;-0.177980;,
    0.241119;0.507290;-0.124581;,
    0.182449;0.507290;-0.184207;,
    0.199746;0.604846;-0.177980;,
    0.241119;0.507290;-0.184207;,
    0.182449;0.507290;-0.124581;,
    0.182449;0.507290;-0.184207;,
    0.235337;0.843173;0.259749;,
    0.235337;0.824071;0.259749;,
    0.235337;0.824071;0.264002;,
    0.235337;0.843173;0.264002;,
    0.260837;0.747531;0.174157;,
    0.260837;0.888090;0.174157;,
    0.260837;0.747531;0.276143;,
    0.260837;0.888090;0.276143;,
    0.260837;0.821035;0.266181;,
    0.206608;0.821035;0.266181;,
    0.206608;0.821035;0.258058;,
    0.260837;0.821035;0.258058;,
    0.260837;0.845918;0.258058;,
    0.206608;0.845918;0.258058;,
    0.206608;0.845918;0.266181;,
    0.260837;0.845918;0.266181;,
    0.260837;0.388964;-0.093289;,
    0.020315;0.388964;-0.093289;,
    0.246650;0.524061;-0.214373;,
    0.020315;0.524061;-0.214373;,
    0.020315;0.524061;-0.093289;,
    0.260837;0.524061;-0.093289;,
    0.020315;0.388964;-0.214373;,
    0.246650;0.388964;-0.214373;,
    0.263173;0.678122;0.146743;,
    0.259557;0.687417;0.146743;,
    0.211518;0.810898;-0.231977;,
    0.211518;0.810898;0.146743;,
    0.263173;0.678122;-0.231977;,
    -0.274837;0.069057;-0.297312;,
    -0.274837;1.124066;0.310312;,
    -0.274837;0.069057;0.297312;,
    -0.274837;1.124066;0.310312;,
    -0.274837;0.060076;0.310312;,
    -0.274837;0.060076;-0.310312;,
    -0.274837;0.060076;0.297312;,
    -0.274837;0.060076;-0.297312;,
    0.206608;0.845918;0.266181;,
    0.206608;0.845918;0.258058;,
    0.206608;0.821035;0.258058;,
    0.206608;0.821035;0.266181;,
    0.020315;0.524061;-0.093289;,
    0.020315;0.524061;-0.214373;,
    0.020315;0.388964;-0.214373;,
    0.020315;0.388964;-0.093289;,
    0.260837;0.821035;0.258058;,
    0.260837;0.845918;0.258058;,
    0.260837;0.821035;0.266181;,
    0.260837;0.845918;0.266181;;
    129;
    3;0,2,1;,
    3;3,5,4;,
    3;6,8,7;,
    3;9,11,10;,
    3;12,14,13;,
    3;12,15,14;,
    3;16,18,17;,
    3;16,19,18;,
    3;20,22,21;,
    3;20,23,22;,
    3;24,26,25;,
    3;24,27,26;,
    3;28,30,29;,
    3;28,31,30;,
    3;32,34,33;,
    3;32,35,34;,
    3;36,38,37;,
    3;36,39,38;,
    3;40,42,41;,
    3;40,43,42;,
    3;44,46,45;,
    3;44,47,46;,
    3;44,48,47;,
    3;44,49,48;,
    3;44,50,49;,
    3;44,51,50;,
    3;52,54,53;,
    3;52,55,54;,
    3;52,56,55;,
    3;52,57,56;,
    3;52,58,57;,
    3;52,59,58;,
    3;60,62,61;,
    3;60,63,62;,
    3;60,64,63;,
    3;60,65,64;,
    3;60,66,65;,
    3;60,67,66;,
    3;68,70,69;,
    3;68,71,70;,
    3;68,72,71;,
    3;68,73,72;,
    3;68,74,73;,
    3;68,75,74;,
    3;76,78,77;,
    3;76,79,78;,
    3;76,80,79;,
    3;76,81,80;,
    3;76,82,81;,
    3;76,83,82;,
    3;84,86,85;,
    3;84,87,86;,
    3;84,88,87;,
    3;84,89,88;,
    3;84,90,89;,
    3;84,91,90;,
    3;92,94,93;,
    3;92,95,94;,
    3;92,96,95;,
    3;92,97,96;,
    3;92,98,97;,
    3;92,99,98;,
    3;100,102,101;,
    3;100,103,102;,
    3;100,104,103;,
    3;100,105,104;,
    3;100,106,105;,
    3;100,107,106;,
    3;140,142,141;,
    3;140,143,142;,
    3;144,146,145;,
    3;144,147,146;,
    3;148,108,149;,
    3;150,109,151;,
    3;109,153,152;,
    3;108,154,149;,
    3;108,155,154;,
    3;151,109,152;,
    3;156,111,110;,
    3;156,157,111;,
    3;158,113,112;,
    3;158,8,113;,
    3;159,112,114;,
    3;159,158,112;,
    3;10,116,115;,
    3;10,160,116;,
    3;8,115,113;,
    3;8,10,115;,
    3;157,117,111;,
    3;157,3,117;,
    3;115,117,113;,
    3;115,111,117;,
    3;113,114,112;,
    3;113,117,114;,
    3;111,116,110;,
    3;111,115,116;,
    3;117,159,114;,
    3;117,3,159;,
    3;161,119,118;,
    3;161,162,119;,
    3;161,163,162;,
    3;163,165,164;,
    3;118,166,161;,
    3;167,165,163;,
    3;168,161,166;,
    3;169,171,170;,
    3;169,172,171;,
    3;173,175,174;,
    3;173,176,175;,
    3;120,122,121;,
    3;120,123,122;,
    3;124,121,125;,
    3;124,126,121;,
    3;127,125,128;,
    3;127,129,125;,
    3;130,128,122;,
    3;130,131,128;,
    3;125,122,128;,
    3;125,121,122;,
    3;132,134,133;,
    3;132,135,134;,
    3;136,178,177;,
    3;136,137,178;,
    3;138,177,179;,
    3;138,136,177;,
    3;137,180,178;,
    3;137,139,180;,
    3;139,179,180;,
    3;139,138,179;;

    MeshNormals {
     181;
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.999476;0.032382;-0.000000;,
     0.965534;0.260278;-0.000000;,
     0.965534;0.260278;-0.000000;,
     0.999476;0.032382;-0.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.000000;0.000000;1.000000;,
     0.899337;0.437256;-0.000000;,
     0.635927;0.437256;0.635927;,
     0.000000;0.437256;0.899337;,
     -0.635927;0.437256;0.635927;,
     -0.899337;0.437256;-0.000000;,
     -0.635927;0.437256;-0.635927;,
     0.000000;0.437256;-0.899337;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;-0.512997;-0.858390;,
     -0.606974;-0.512997;-0.606974;,
     -0.858390;-0.512997;-0.000000;,
     -0.606974;-0.512997;0.606974;,
     0.000000;-0.512997;0.858390;,
     0.606974;-0.512997;0.606974;,
     0.858390;-0.512997;-0.000000;,
     0.899337;0.437256;-0.000000;,
     0.635927;0.437256;0.635927;,
     -0.000000;0.437256;0.899337;,
     -0.635927;0.437256;0.635927;,
     -0.899337;0.437256;-0.000000;,
     -0.635927;0.437256;-0.635927;,
     -0.000000;0.437256;-0.899337;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     -0.000000;-0.512997;-0.858390;,
     -0.606974;-0.512997;-0.606974;,
     -0.858390;-0.512997;-0.000000;,
     -0.606974;-0.512997;0.606974;,
     -0.000000;-0.512997;0.858390;,
     0.606974;-0.512997;0.606974;,
     0.858390;-0.512997;-0.000000;,
     0.899337;0.437256;-0.000000;,
     0.635927;0.437256;0.635927;,
     0.000000;0.437256;0.899337;,
     -0.635927;0.437256;0.635927;,
     -0.899337;0.437256;-0.000000;,
     -0.635927;0.437256;-0.635927;,
     0.000000;0.437256;-0.899337;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;-0.512997;-0.858390;,
     -0.606974;-0.512997;-0.606974;,
     -0.858390;-0.512997;-0.000000;,
     -0.606974;-0.512997;0.606974;,
     0.000000;-0.512997;0.858390;,
     0.606974;-0.512997;0.606974;,
     0.858390;-0.512997;-0.000000;,
     0.899337;0.437256;-0.000000;,
     0.635927;0.437256;0.635927;,
     0.000000;0.437256;0.899337;,
     -0.635927;0.437256;0.635927;,
     -0.899337;0.437256;-0.000000;,
     -0.635927;0.437256;-0.635927;,
     0.000000;0.437256;-0.899337;,
     0.635927;0.437256;-0.635927;,
     0.606974;-0.512997;-0.606974;,
     0.000000;-0.512997;-0.858390;,
     -0.606974;-0.512997;-0.606974;,
     -0.858390;-0.512997;-0.000000;,
     -0.606974;-0.512997;0.606974;,
     0.000000;-0.512997;0.858390;,
     0.606974;-0.512997;0.606974;,
     0.858390;-0.512997;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     -0.417356;-0.730152;0.541011;,
     -0.658992;-0.256378;0.707107;,
     -0.679690;0.388512;-0.622157;,
     -0.658992;-0.256378;-0.707107;,
     -0.679690;0.388512;0.622157;,
     -0.658992;-0.256378;-0.707107;,
     -0.417356;-0.730152;-0.541011;,
     -0.658992;-0.256378;0.707107;,
     -1.000000;-0.000000;-0.000000;,
     -0.316228;-0.000000;0.948683;,
     -0.386821;-0.836729;-0.387626;,
     -0.546572;-0.633461;-0.547710;,
     0.526404;-0.662001;-0.533529;,
     0.372817;-0.847483;-0.377863;,
     -0.386821;-0.836729;0.387626;,
     -0.546572;-0.633461;0.547710;,
     -0.386821;-0.836729;-0.387626;,
     0.372817;-0.847483;0.377863;,
     0.526404;-0.662001;0.533529;,
     -0.386821;-0.836729;0.387626;,
     0.372817;-0.847483;-0.377863;,
     0.372817;-0.847483;0.377863;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;-0.000016;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;-0.000016;-0.000000;,
     1.000000;0.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.000000;1.000000;-0.000000;,
     0.113504;-0.697938;0.707107;,
     0.000000;0.000000;1.000000;,
     -0.113504;0.697938;-0.707107;,
     -0.113504;0.697938;0.707107;,
     0.113504;-0.697938;-0.707107;,
     -1.000000;-0.000000;-0.000000;,
     -1.000000;-0.000000;-0.000000;,
     -1.000000;-0.000000;-0.000000;,
     -0.707107;0.000000;0.707107;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;,
     1.000000;0.000000;-0.000000;;
     129;
     3;0,2,1;,
     3;3,5,4;,
     3;6,8,7;,
     3;9,11,10;,
     3;12,14,13;,
     3;12,15,14;,
     3;16,18,17;,
     3;16,19,18;,
     3;20,22,21;,
     3;20,23,22;,
     3;24,26,25;,
     3;24,27,26;,
     3;28,30,29;,
     3;28,31,30;,
     3;32,34,33;,
     3;32,35,34;,
     3;36,38,37;,
     3;36,39,38;,
     3;40,42,41;,
     3;40,43,42;,
     3;44,46,45;,
     3;44,47,46;,
     3;44,48,47;,
     3;44,49,48;,
     3;44,50,49;,
     3;44,51,50;,
     3;52,54,53;,
     3;52,55,54;,
     3;52,56,55;,
     3;52,57,56;,
     3;52,58,57;,
     3;52,59,58;,
     3;60,62,61;,
     3;60,63,62;,
     3;60,64,63;,
     3;60,65,64;,
     3;60,66,65;,
     3;60,67,66;,
     3;68,70,69;,
     3;68,71,70;,
     3;68,72,71;,
     3;68,73,72;,
     3;68,74,73;,
     3;68,75,74;,
     3;76,78,77;,
     3;76,79,78;,
     3;76,80,79;,
     3;76,81,80;,
     3;76,82,81;,
     3;76,83,82;,
     3;84,86,85;,
     3;84,87,86;,
     3;84,88,87;,
     3;84,89,88;,
     3;84,90,89;,
     3;84,91,90;,
     3;92,94,93;,
     3;92,95,94;,
     3;92,96,95;,
     3;92,97,96;,
     3;92,98,97;,
     3;92,99,98;,
     3;100,102,101;,
     3;100,103,102;,
     3;100,104,103;,
     3;100,105,104;,
     3;100,106,105;,
     3;100,107,106;,
     3;140,142,141;,
     3;140,143,142;,
     3;144,146,145;,
     3;144,147,146;,
     3;148,108,149;,
     3;150,109,151;,
     3;109,153,152;,
     3;108,154,149;,
     3;108,155,154;,
     3;151,109,152;,
     3;156,111,110;,
     3;156,157,111;,
     3;158,113,112;,
     3;158,8,113;,
     3;159,112,114;,
     3;159,158,112;,
     3;10,116,115;,
     3;10,160,116;,
     3;8,115,113;,
     3;8,10,115;,
     3;157,117,111;,
     3;157,3,117;,
     3;115,117,113;,
     3;115,111,117;,
     3;113,114,112;,
     3;113,117,114;,
     3;111,116,110;,
     3;111,115,116;,
     3;117,159,114;,
     3;117,3,159;,
     3;161,119,118;,
     3;161,162,119;,
     3;161,163,162;,
     3;163,165,164;,
     3;118,166,161;,
     3;167,165,163;,
     3;168,161,166;,
     3;169,171,170;,
     3;169,172,171;,
     3;173,175,174;,
     3;173,176,175;,
     3;120,122,121;,
     3;120,123,122;,
     3;124,121,125;,
     3;124,126,121;,
     3;127,125,128;,
     3;127,129,125;,
     3;130,128,122;,
     3;130,131,128;,
     3;125,122,128;,
     3;125,121,122;,
     3;132,134,133;,
     3;132,135,134;,
     3;136,178,177;,
     3;136,137,178;,
     3;138,177,179;,
     3;138,136,177;,
     3;137,180,178;,
     3;137,139,180;,
     3;139,179,180;,
     3;139,138,179;;
    }

    MeshTextureCoords {
     181;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.354376;0.710529;,
     0.354376;0.710529;,
     0.354376;0.710708;,
     0.354376;0.710708;,
     0.242228;0.710708;,
     0.242228;0.710708;,
     0.242228;0.710529;,
     0.242228;0.710529;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.298302;0.710708;,
     0.298302;0.710529;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.452897;0.257861;,
     0.452897;0.257861;,
     0.525644;0.257861;,
     0.452897;0.257861;,
     0.462159;0.177818;,
     0.462159;0.177818;,
     0.462159;0.177818;,
     0.525644;0.177818;,
     0.525644;0.177818;,
     0.525644;0.177818;,
     0.525644;0.257861;,
     0.525644;0.257861;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.354376;0.710708;,
     0.354376;0.710708;,
     0.242228;0.710529;,
     0.242228;0.710529;,
     0.354376;0.710529;,
     0.354376;0.710529;,
     0.242228;0.710708;,
     0.242228;0.710708;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.354376;0.710529;,
     0.242228;0.710529;,
     0.242228;0.710708;,
     0.354376;0.710708;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     181;
     140;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     16,
     17,
     18,
     19,
     20,
     21,
     22,
     23,
     24,
     25,
     26,
     27,
     28,
     29,
     30,
     31,
     32,
     33,
     34,
     35,
     36,
     37,
     38,
     39,
     40,
     41,
     42,
     43,
     44,
     45,
     46,
     47,
     48,
     49,
     50,
     51,
     52,
     53,
     54,
     55,
     56,
     57,
     58,
     59,
     60,
     61,
     62,
     63,
     64,
     65,
     66,
     67,
     68,
     69,
     70,
     71,
     72,
     73,
     74,
     75,
     76,
     77,
     78,
     79,
     80,
     81,
     82,
     83,
     84,
     85,
     86,
     87,
     88,
     89,
     90,
     91,
     92,
     93,
     94,
     95,
     96,
     97,
     98,
     99,
     100,
     101,
     102,
     103,
     104,
     105,
     106,
     107,
     108,
     109,
     110,
     111,
     112,
     113,
     114,
     115,
     116,
     117,
     118,
     119,
     120,
     121,
     122,
     123,
     124,
     125,
     126,
     127,
     128,
     129,
     130,
     131,
     132,
     133,
     134,
     135,
     136,
     137,
     138,
     139,
     31,
     30,
     33,
     32,
     35,
     34,
     29,
     28,
     39,
     38,
     43,
     42,
     37,
     36,
     41,
     40,
     0,
     1,
     7,
     4,
     11,
     15,
     119,
     12,
     119,
     16,
     21,
     19,
     22,
     29,
     34,
     33,
     30,
     37,
     42,
     41,
     38,
     32,
     35,
     31,
     28;
    }

    MeshMaterialList {
     1;
     129;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame default {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    22;
    0.260837;0.069057;-0.297312;,
    0.260837;0.069057;0.297312;,
    0.260837;0.069057;0.297312;,
    0.260837;0.069057;-0.297312;,
    0.203725;0.744393;0.175531;,
    0.203725;0.749056;0.170868;,
    0.203725;0.887748;0.170868;,
    0.203725;0.892410;0.175531;,
    0.203725;0.892410;0.277191;,
    0.203725;0.887748;0.281853;,
    0.203725;0.749056;0.282801;,
    0.203725;0.744393;0.278139;,
    -0.274837;1.124066;0.310312;,
    -0.274837;0.069057;0.297312;,
    0.274837;0.060076;0.297312;,
    0.274837;0.060076;-0.297312;,
    -0.274837;1.124066;0.310312;,
    -0.274837;1.124066;0.310312;,
    0.260837;0.069057;0.297312;,
    0.260837;0.069057;0.297312;,
    0.260837;0.069057;-0.297312;,
    0.260837;0.069057;-0.297312;;
    12;
    3;0,2,1;,
    3;0,3,2;,
    3;4,6,5;,
    3;4,7,6;,
    3;4,8,7;,
    3;4,9,8;,
    3;4,10,9;,
    3;4,11,10;,
    3;12,13,16;,
    3;17,12,16;,
    3;18,14,19;,
    3;15,21,20;;

    MeshNormals {
     22;
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     0.000000;-1.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;0.000000;-0.000000;,
     -1.000000;-0.000000;-0.000000;,
     -1.000000;-0.000000;-0.000000;,
     -0.000000;0.000000;-1.000000;,
     -0.000000;0.000000;1.000000;,
     -0.707107;0.000000;0.707107;,
     -0.316228;-0.000000;0.948683;,
     -0.000000;0.000000;-1.000000;,
     0.000000;0.000000;-1.000000;,
     0.000000;0.000000;1.000000;,
     -0.000000;0.000000;1.000000;;
     12;
     3;0,2,1;,
     3;0,3,2;,
     3;4,6,5;,
     3;4,7,6;,
     3;4,8,7;,
     3;4,9,8;,
     3;4,10,9;,
     3;4,11,10;,
     3;12,13,16;,
     3;17,12,16;,
     3;18,14,19;,
     3;15,21,20;;
    }

    MeshTextureCoords {
     22;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     22;
     16;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8,
     9,
     10,
     11,
     12,
     13,
     14,
     15,
     12,
     12,
     2,
     1,
     0,
     3;
    }

    MeshMaterialList {
     1;
     12;
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0,
     0;

     Material default {
      1.000000;1.000000;1.000000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.148069;,
    0.231852;0.711091;-0.150738;,
    0.226601;0.724588;-0.150738;,
    0.225633;0.727075;-0.148069;,
    0.225633;0.727075;-0.134499;,
    0.226601;0.724588;-0.131830;,
    0.231852;0.711091;-0.131830;,
    0.232819;0.708604;-0.134499;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;0.005413;,
    0.231852;0.711091;0.002744;,
    0.226601;0.724588;0.002744;,
    0.225633;0.727075;0.005413;,
    0.225633;0.727075;0.018984;,
    0.226601;0.724588;0.021652;,
    0.231852;0.711091;0.021652;,
    0.232819;0.708604;0.018984;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.126143;,
    0.231852;0.711091;-0.128812;,
    0.226601;0.724588;-0.128812;,
    0.225633;0.727075;-0.126143;,
    0.225633;0.727075;-0.112572;,
    0.226601;0.724588;-0.109904;,
    0.231852;0.711091;-0.109904;,
    0.232819;0.708604;-0.112572;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.148069;,
    0.223861;0.731631;-0.150738;,
    0.218610;0.745128;-0.150738;,
    0.217642;0.747615;-0.148069;,
    0.217642;0.747615;-0.134499;,
    0.218610;0.745128;-0.131830;,
    0.223861;0.731631;-0.131830;,
    0.224828;0.729144;-0.134499;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;0.005413;,
    0.223861;0.731631;0.002744;,
    0.218610;0.745128;0.002744;,
    0.217642;0.747615;0.005413;,
    0.217642;0.747615;0.018984;,
    0.218610;0.745128;0.021652;,
    0.223861;0.731631;0.021652;,
    0.224828;0.729144;0.018984;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.126143;,
    0.223861;0.731631;-0.128812;,
    0.218610;0.745128;-0.128812;,
    0.217642;0.747615;-0.126143;,
    0.217642;0.747615;-0.112572;,
    0.218610;0.745128;-0.109904;,
    0.223861;0.731631;-0.109904;,
    0.224828;0.729144;-0.112572;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.148069;,
    0.215870;0.752171;-0.150738;,
    0.210619;0.765668;-0.150738;,
    0.209651;0.768155;-0.148069;,
    0.209651;0.768155;-0.134499;,
    0.210619;0.765668;-0.131830;,
    0.215870;0.752171;-0.131830;,
    0.216837;0.749684;-0.134499;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;0.005413;,
    0.215870;0.752171;0.002744;,
    0.210619;0.765668;0.002744;,
    0.209651;0.768155;0.005413;,
    0.209651;0.768155;0.018984;,
    0.210619;0.765668;0.021652;,
    0.215870;0.752171;0.021652;,
    0.216837;0.749684;0.018984;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.126143;,
    0.215870;0.752171;-0.128812;,
    0.210619;0.765668;-0.128812;,
    0.209651;0.768155;-0.126143;,
    0.209651;0.768155;-0.112572;,
    0.210619;0.765668;-0.109904;,
    0.215870;0.752171;-0.109904;,
    0.216837;0.749684;-0.112572;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.148069;,
    0.207879;0.772712;-0.150738;,
    0.202628;0.786209;-0.150738;,
    0.201660;0.788696;-0.148069;,
    0.201660;0.788696;-0.134499;,
    0.202628;0.786209;-0.131830;,
    0.207879;0.772712;-0.131830;,
    0.208846;0.770225;-0.134499;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;0.005413;,
    0.207879;0.772712;0.002744;,
    0.202628;0.786209;0.002744;,
    0.201660;0.788696;0.005413;,
    0.201660;0.788696;0.018984;,
    0.202628;0.786209;0.021652;,
    0.207879;0.772712;0.021652;,
    0.208846;0.770225;0.018984;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.126143;,
    0.207879;0.772712;-0.128812;,
    0.202628;0.786209;-0.128812;,
    0.201660;0.788696;-0.126143;,
    0.201660;0.788696;-0.112572;,
    0.202628;0.786209;-0.109904;,
    0.207879;0.772712;-0.109904;,
    0.208846;0.770225;-0.112572;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.104217;,
    0.207879;0.772712;-0.106886;,
    0.202628;0.786209;-0.106886;,
    0.201660;0.788696;-0.104217;,
    0.201660;0.788696;-0.090646;,
    0.202628;0.786209;-0.087978;,
    0.207879;0.772712;-0.087978;,
    0.208846;0.770225;-0.090646;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.082291;,
    0.207879;0.772712;-0.084960;,
    0.202628;0.786209;-0.084960;,
    0.201660;0.788696;-0.082291;,
    0.201660;0.788696;-0.068720;,
    0.202628;0.786209;-0.066052;,
    0.207879;0.772712;-0.066052;,
    0.208846;0.770225;-0.068720;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.060365;,
    0.207879;0.772712;-0.063034;,
    0.202628;0.786209;-0.063034;,
    0.201660;0.788696;-0.060365;,
    0.201660;0.788696;-0.046794;,
    0.202628;0.786209;-0.044126;,
    0.207879;0.772712;-0.044126;,
    0.208846;0.770225;-0.046794;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.038439;,
    0.207879;0.772712;-0.041108;,
    0.202628;0.786209;-0.041108;,
    0.201660;0.788696;-0.038439;,
    0.201660;0.788696;-0.024868;,
    0.202628;0.786209;-0.022200;,
    0.207879;0.772712;-0.022200;,
    0.208846;0.770225;-0.024868;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;-0.016513;,
    0.207879;0.772712;-0.019182;,
    0.202628;0.786209;-0.019182;,
    0.201660;0.788696;-0.016513;,
    0.201660;0.788696;-0.002942;,
    0.202628;0.786209;-0.000274;,
    0.207879;0.772712;-0.000274;,
    0.208846;0.770225;-0.002942;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;0.027339;,
    0.207879;0.772712;0.024670;,
    0.202628;0.786209;0.024670;,
    0.201660;0.788696;0.027339;,
    0.201660;0.788696;0.040910;,
    0.202628;0.786209;0.043578;,
    0.207879;0.772712;0.043578;,
    0.208846;0.770225;0.040910;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.208846;0.770225;0.049265;,
    0.207879;0.772712;0.046596;,
    0.202628;0.786209;0.046596;,
    0.201660;0.788696;0.049265;,
    0.201660;0.788696;0.062835;,
    0.202628;0.786209;0.065504;,
    0.207879;0.772712;0.065504;,
    0.208846;0.770225;0.062835;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.104217;,
    0.215870;0.752171;-0.106886;,
    0.210619;0.765668;-0.106886;,
    0.209651;0.768155;-0.104217;,
    0.209651;0.768155;-0.090646;,
    0.210619;0.765668;-0.087978;,
    0.215870;0.752171;-0.087978;,
    0.216837;0.749684;-0.090646;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.082291;,
    0.215870;0.752171;-0.084960;,
    0.210619;0.765668;-0.084960;,
    0.209651;0.768155;-0.082291;,
    0.209651;0.768155;-0.068720;,
    0.210619;0.765668;-0.066052;,
    0.215870;0.752171;-0.066052;,
    0.216837;0.749684;-0.068720;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.060365;,
    0.215870;0.752171;-0.063034;,
    0.210619;0.765668;-0.063034;,
    0.209651;0.768155;-0.060365;,
    0.209651;0.768155;-0.046794;,
    0.210619;0.765668;-0.044126;,
    0.215870;0.752171;-0.044126;,
    0.216837;0.749684;-0.046794;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.038439;,
    0.215870;0.752171;-0.041108;,
    0.210619;0.765668;-0.041108;,
    0.209651;0.768155;-0.038439;,
    0.209651;0.768155;-0.024868;,
    0.210619;0.765668;-0.022200;,
    0.215870;0.752171;-0.022200;,
    0.216837;0.749684;-0.024868;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;-0.016513;,
    0.215870;0.752171;-0.019182;,
    0.210619;0.765668;-0.019182;,
    0.209651;0.768155;-0.016513;,
    0.209651;0.768155;-0.002942;,
    0.210619;0.765668;-0.000274;,
    0.215870;0.752171;-0.000274;,
    0.216837;0.749684;-0.002942;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;0.027339;,
    0.215870;0.752171;0.024670;,
    0.210619;0.765668;0.024670;,
    0.209651;0.768155;0.027339;,
    0.209651;0.768155;0.040910;,
    0.210619;0.765668;0.043578;,
    0.215870;0.752171;0.043578;,
    0.216837;0.749684;0.040910;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.216837;0.749684;0.049265;,
    0.215870;0.752171;0.046596;,
    0.210619;0.765668;0.046596;,
    0.209651;0.768155;0.049265;,
    0.209651;0.768155;0.062835;,
    0.210619;0.765668;0.065504;,
    0.215870;0.752171;0.065504;,
    0.216837;0.749684;0.062835;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.104217;,
    0.223861;0.731631;-0.106886;,
    0.218610;0.745128;-0.106886;,
    0.217642;0.747615;-0.104217;,
    0.217642;0.747615;-0.090646;,
    0.218610;0.745128;-0.087978;,
    0.223861;0.731631;-0.087978;,
    0.224828;0.729144;-0.090646;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.082291;,
    0.223861;0.731631;-0.084960;,
    0.218610;0.745128;-0.084960;,
    0.217642;0.747615;-0.082291;,
    0.217642;0.747615;-0.068720;,
    0.218610;0.745128;-0.066052;,
    0.223861;0.731631;-0.066052;,
    0.224828;0.729144;-0.068720;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.060365;,
    0.223861;0.731631;-0.063034;,
    0.218610;0.745128;-0.063034;,
    0.217642;0.747615;-0.060365;,
    0.217642;0.747615;-0.046794;,
    0.218610;0.745128;-0.044126;,
    0.223861;0.731631;-0.044126;,
    0.224828;0.729144;-0.046794;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.038439;,
    0.223861;0.731631;-0.041108;,
    0.218610;0.745128;-0.041108;,
    0.217642;0.747615;-0.038439;,
    0.217642;0.747615;-0.024868;,
    0.218610;0.745128;-0.022200;,
    0.223861;0.731631;-0.022200;,
    0.224828;0.729144;-0.024868;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;-0.016513;,
    0.223861;0.731631;-0.019182;,
    0.218610;0.745128;-0.019182;,
    0.217642;0.747615;-0.016513;,
    0.217642;0.747615;-0.002942;,
    0.218610;0.745128;-0.000274;,
    0.223861;0.731631;-0.000274;,
    0.224828;0.729144;-0.002942;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;0.027339;,
    0.223861;0.731631;0.024670;,
    0.218610;0.745128;0.024670;,
    0.217642;0.747615;0.027339;,
    0.217642;0.747615;0.040910;,
    0.218610;0.745128;0.043578;,
    0.223861;0.731631;0.043578;,
    0.224828;0.729144;0.040910;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.224828;0.729144;0.049265;,
    0.223861;0.731631;0.046596;,
    0.218610;0.745128;0.046596;,
    0.217642;0.747615;0.049265;,
    0.217642;0.747615;0.062835;,
    0.218610;0.745128;0.065504;,
    0.223861;0.731631;0.065504;,
    0.224828;0.729144;0.062835;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.104217;,
    0.231852;0.711091;-0.106886;,
    0.226601;0.724588;-0.106886;,
    0.225633;0.727075;-0.104217;,
    0.225633;0.727075;-0.090646;,
    0.226601;0.724588;-0.087978;,
    0.231852;0.711091;-0.087978;,
    0.232819;0.708604;-0.090646;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.082291;,
    0.231852;0.711091;-0.084960;,
    0.226601;0.724588;-0.084960;,
    0.225633;0.727075;-0.082291;,
    0.225633;0.727075;-0.068720;,
    0.226601;0.724588;-0.066052;,
    0.231852;0.711091;-0.066052;,
    0.232819;0.708604;-0.068720;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.060365;,
    0.231852;0.711091;-0.063034;,
    0.226601;0.724588;-0.063034;,
    0.225633;0.727075;-0.060365;,
    0.225633;0.727075;-0.046794;,
    0.226601;0.724588;-0.044126;,
    0.231852;0.711091;-0.044126;,
    0.232819;0.708604;-0.046794;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.038439;,
    0.231852;0.711091;-0.041108;,
    0.226601;0.724588;-0.041108;,
    0.225633;0.727075;-0.038439;,
    0.225633;0.727075;-0.024868;,
    0.226601;0.724588;-0.022200;,
    0.231852;0.711091;-0.022200;,
    0.232819;0.708604;-0.024868;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;-0.016513;,
    0.231852;0.711091;-0.019182;,
    0.226601;0.724588;-0.019182;,
    0.225633;0.727075;-0.016513;,
    0.225633;0.727075;-0.002942;,
    0.226601;0.724588;-0.000274;,
    0.231852;0.711091;-0.000274;,
    0.232819;0.708604;-0.002942;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;0.027339;,
    0.231852;0.711091;0.024670;,
    0.226601;0.724588;0.024670;,
    0.225633;0.727075;0.027339;,
    0.225633;0.727075;0.040910;,
    0.226601;0.724588;0.043578;,
    0.231852;0.711091;0.043578;,
    0.232819;0.708604;0.040910;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.232819;0.708604;0.049265;,
    0.231852;0.711091;0.046596;,
    0.226601;0.724588;0.046596;,
    0.225633;0.727075;0.049265;,
    0.225633;0.727075;0.062835;,
    0.226601;0.724588;0.065504;,
    0.231852;0.711091;0.065504;,
    0.232819;0.708604;0.062835;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;,
     -0.931955;-0.362573;-0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
  Frame Black {

   FrameTransformMatrix {
    1.000000, 0.000000, 0.000000, 0.000000,
    0.000000, 1.000000, 0.000000, 0.000000,
    0.000000, 0.000000, 1.000000, 0.000000,
    0.000000, 0.000000, 0.000000, 1.000000;;
   }

   Mesh {
    8;
    0.240811;0.688063;-0.148069;,
    0.239843;0.690550;-0.150738;,
    0.234592;0.704047;-0.150738;,
    0.233625;0.706534;-0.148069;,
    0.233625;0.706534;0.062836;,
    0.234592;0.704047;0.065504;,
    0.239843;0.690550;0.065504;,
    0.240811;0.688063;0.062836;;
    6;
    3;0,2,1;,
    3;0,3,2;,
    3;0,4,3;,
    3;0,5,4;,
    3;0,6,5;,
    3;0,7,6;;

    MeshNormals {
     8;
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;,
     -0.931955;-0.362573;0.000000;;
     6;
     3;0,2,1;,
     3;0,3,2;,
     3;0,4,3;,
     3;0,5,4;,
     3;0,6,5;,
     3;0,7,6;;
    }

    MeshTextureCoords {
     8;
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;,
     0.000000;0.000000;;
    }

    VertexDuplicationIndices {
     8;
     8;
     0,
     1,
     2,
     3,
     4,
     5,
     6,
     7;
    }

    MeshMaterialList {
     1;
     6;
     0,
     0,
     0,
     0,
     0,
     0;

     Material Black {
      0.001000;0.001000;0.001000;1.000000;;
      0.100000;
      1.000000;1.000000;1.000000;;
      0.000000;0.000000;0.000000;;
     }
    }

    XSkinMeshHeader {
     1;
     3;
     0;
    }

   }
 }
 }
}

AnimationSet AnimationSet0
{
 Animation
 {
  AnimationKey
  {
   4;
   2;
   0; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;,
   -1; 16; 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000;;;
  }
  { Scene_Root }
 }
}

